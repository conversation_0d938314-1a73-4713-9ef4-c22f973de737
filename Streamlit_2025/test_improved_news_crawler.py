#!/usr/bin/env python3
"""
Test script to verify the improved news crawler functionality
"""

import sys
import os
import asyncio
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_topic_filtering():
    """Test topic filtering functionality"""
    print("🧪 Testing Topic Filtering...")
    
    try:
        from enhanced_crawl_news import EnhancedNewsCrawler
        
        crawler = EnhancedNewsCrawler()
        print("✅ EnhancedNewsCrawler initialized")
        
        # Test sample data with topic filtering
        sample_articles = [
            {
                "TITLE": "Thị trường chứng khoán tăng mạnh",
                "LINK": "https://example.com/1",
                "TIME": "2024-01-01",
                "SUMMARY": "Chỉ số VN-Index tăng 2%",
                "CONTENT": "Thị trường chứng khoán Việt Nam có phiên tăng mạnh..."
            },
            {
                "TITLE": "Ngân hàng công bố lãi suất mới",
                "LINK": "https://example.com/2", 
                "TIME": "2024-01-01",
                "SUMMARY": "Các ngân hàng điều chỉnh lãi suất",
                "CONTENT": "Nhiều ngân hàng thương mại đã công bố..."
            },
            {
                "TITLE": "Bất động sản TP.HCM phục hồi",
                "LINK": "https://example.com/3",
                "TIME": "2024-01-01", 
                "SUMMARY": "Thị trường bất động sản có dấu hiệu tích cực",
                "CONTENT": "Thị trường bất động sản TP.HCM đang có những..."
            }
        ]
        
        # Test filtering logic
        topic_filter = "chứng khoán"
        filtered_articles = []
        
        for article in sample_articles:
            title_text = article["TITLE"].lower()
            summary_text = article["SUMMARY"].lower()
            content_text = article["CONTENT"].lower()
            topic_lower = topic_filter.lower()
            
            if (topic_lower in title_text or 
                topic_lower in summary_text or 
                topic_lower in content_text):
                filtered_articles.append(article)
        
        print(f"✅ Original articles: {len(sample_articles)}")
        print(f"✅ Filtered articles ('{topic_filter}'): {len(filtered_articles)}")
        
        # Should find 1 article about "chứng khoán"
        if len(filtered_articles) == 1:
            print("✅ Topic filtering working correctly")
            return True
        else:
            print(f"❌ Expected 1 filtered article, got {len(filtered_articles)}")
            return False
        
    except Exception as e:
        print(f"❌ Topic filtering test failed: {str(e)}")
        return False

def test_interface_improvements():
    """Test interface improvements"""
    print("\n🧪 Testing Interface Improvements...")
    
    try:
        # Read the enhanced file and check for improvements
        with open('enhanced_crawl_news.py', 'r') as f:
            content = f.read()
        
        # Check that duplication is removed
        improvements_to_check = [
            # Should NOT have sidebar source selection
            'st.sidebar.radio' not in content or content.count('st.sidebar.radio') <= 1,
            
            # Should have topic search input
            'topic_filter = st.text_input' in content,
            
            # Should have unified crawling button
            'unified_crawl_btn' in content,
            
            # Should have configuration info display
            'config_info' in content,
            
            # Should have topic filtering in crawl methods
            'topic_filter=' in content,
            
            # Should have topic tips
            'Topic Search Tips' in content
        ]
        
        improvement_names = [
            "Removed sidebar duplication",
            "Added topic search input", 
            "Added unified crawling button",
            "Added configuration info display",
            "Added topic filtering to crawl methods",
            "Added topic search tips"
        ]
        
        for i, (improvement, name) in enumerate(zip(improvements_to_check, improvement_names)):
            if improvement:
                print(f"✅ {name}")
            else:
                print(f"❌ {name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Interface improvements test failed: {str(e)}")
        return False

def test_unified_crawling_logic():
    """Test unified crawling logic"""
    print("\n🧪 Testing Unified Crawling Logic...")
    
    try:
        from enhanced_crawl_news import EnhancedNewsCrawler
        
        crawler = EnhancedNewsCrawler()
        
        # Test that all crawl methods accept topic_filter parameter
        import inspect
        
        methods_to_check = [
            'crawl_thoibaotaichinh',
            'crawl_vneconomy',
            'crawl_both_sources'
        ]
        
        for method_name in methods_to_check:
            method = getattr(crawler, method_name)
            signature = inspect.signature(method)
            
            if 'topic_filter' in signature.parameters:
                print(f"✅ {method_name} accepts topic_filter parameter")
            else:
                print(f"❌ {method_name} missing topic_filter parameter")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Unified crawling logic test failed: {str(e)}")
        return False

def test_no_duplication():
    """Test that duplication is removed"""
    print("\n🧪 Testing Duplication Removal...")
    
    try:
        with open('enhanced_crawl_news.py', 'r') as f:
            content = f.read()
        
        # Count occurrences of potentially duplicated elements
        duplication_checks = [
            # Should have only one source selection (not in sidebar)
            (content.count('Select news source'), 1, "Source selection"),

            # Should not have multiple crawl buttons for same functionality
            (content.count('"🏢 Crawl Thời Báo"'), 0, "Separate Thoi Bao button"),
            (content.count('"📈 Crawl VnEconomy"'), 0, "Separate VnEconomy button"),
            (content.count('"🔄 Crawl Both"'), 0, "Separate Both button"),

            # Should have unified crawling
            (content.count('Start Crawling'), 4, "Unified crawl button text (1 subheader + 3 button texts)"),

            # Should not have sidebar configuration
            (content.count('st.sidebar.subheader'), 0, "Sidebar configuration sections")
        ]
        
        for count, expected, description in duplication_checks:
            if count == expected:
                print(f"✅ {description}: {count} (expected {expected})")
            else:
                print(f"❌ {description}: {count} (expected {expected})")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Duplication removal test failed: {str(e)}")
        return False

def test_topic_search_features():
    """Test topic search features"""
    print("\n🧪 Testing Topic Search Features...")
    
    try:
        with open('enhanced_crawl_news.py', 'r') as f:
            content = f.read()
        
        # Check for topic search features
        topic_features = [
            'topic_filter = st.text_input',
            'Search for specific topic',
            'placeholder="e.g., chứng khoán',
            'Topic Search Tips',
            'filtered by:',
            'topic_filter if topic_filter else',
            'about \'{topic_filter}\'',
        ]
        
        for feature in topic_features:
            if feature in content:
                print(f"✅ Found topic feature: {feature[:30]}...")
            else:
                print(f"❌ Missing topic feature: {feature[:30]}...")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Topic search features test failed: {str(e)}")
        return False

def test_improved_user_experience():
    """Test improved user experience elements"""
    print("\n🧪 Testing Improved User Experience...")
    
    try:
        with open('enhanced_crawl_news.py', 'r') as f:
            content = f.read()
        
        # Check for UX improvements
        ux_improvements = [
            # Configuration display
            'config_info = f"**Source:**',
            
            # Helpful tips
            'Topic Search Tips',
            'Crawling Info',
            
            # Better feedback messages
            'articles about \'{topic_filter}\'',
            'No articles found about \'{topic_filter}\'',
            
            # Clear configuration display
            'st.info(config_info)',
            
            # Better file naming
            'file_suffix = f"_{topic_filter.replace',
        ]
        
        for improvement in ux_improvements:
            if improvement in content:
                print(f"✅ Found UX improvement: {improvement[:40]}...")
            else:
                print(f"❌ Missing UX improvement: {improvement[:40]}...")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Improved user experience test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Improved News Crawler...\n")
    
    # Run tests
    topic_ok = test_topic_filtering()
    interface_ok = test_interface_improvements()
    unified_ok = test_unified_crawling_logic()
    no_dup_ok = test_no_duplication()
    topic_search_ok = test_topic_search_features()
    ux_ok = test_improved_user_experience()
    
    print("\n🎉 Improved News Crawler Testing Completed!")
    
    if topic_ok and interface_ok and unified_ok and no_dup_ok and topic_search_ok and ux_ok:
        print("\n✅ All tests passed! The improved news crawler is working correctly.")
        print("\n📋 Summary of Improvements:")
        print("✅ Removed duplication between sidebar and main section")
        print("✅ Added topic search and filtering functionality")
        print("✅ Unified crawling interface with single button")
        print("✅ Enhanced user experience with better feedback")
        print("✅ Improved configuration display")
        print("✅ Added helpful tips and guidance")
        
        print("\n🚀 Key Improvements:")
        print("- 🔍 Topic search: Filter articles by keywords")
        print("- 🎯 Unified interface: No more confusing duplicate controls")
        print("- 📊 Better feedback: Clear status and configuration display")
        print("- 💡 User guidance: Tips and examples for better usage")
        print("- 📁 Smart exports: Topic-based file naming")
        
        print("\n💡 How to use the improved interface:")
        print("1. Select your news source(s)")
        print("2. Enter a topic to search for (optional)")
        print("3. Configure VnEconomy pages if needed")
        print("4. Click the single 'Start Crawling' button")
        print("5. View filtered results with topic information")
        
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
        
        if not topic_ok:
            print("- Fix topic filtering logic")
        if not interface_ok:
            print("- Fix interface improvements")
        if not unified_ok:
            print("- Fix unified crawling logic")
        if not no_dup_ok:
            print("- Remove remaining duplications")
        if not topic_search_ok:
            print("- Fix topic search features")
        if not ux_ok:
            print("- Fix user experience improvements")

if __name__ == "__main__":
    main()
