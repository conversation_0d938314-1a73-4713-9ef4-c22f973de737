# 🔧 Admin Page Access Guide

## 📍 **Where is the Admin Page?**

The admin page is **dynamically added** to the navigation menu when you log in with an admin account. It's not visible to regular users for security reasons.

## 🔑 **How to Access the Admin Page**

### **Step 1: Start the App**
```bash
streamlit run app.py
```

### **Step 2: Login with Admin Account**
- **Username:** `admin`
- **Password:** Your admin password
- **Email:** `<EMAIL>`

### **Step 3: Look for Admin Dashboard**
After successful login, you should see:
- **🔧 Admin Dashboard** in the navigation menu (sidebar or main navigation)
- This page will ONLY appear if you're logged in as an admin user

## 👤 **Admin vs Regular Users**

### **Admin User (`admin`):**
- ✅ **Has admin role** in config.yaml
- ✅ **Can see "🔧 Admin Dashboard"** in navigation
- ✅ **Full access** to all admin features
- ✅ **User analytics, support tickets, system management**

### **Regular Users (`hector29`, `hungvu`, `test3`):**
- ❌ **No admin role** in config.yaml
- ❌ **Cannot see Admin Dashboard** in navigation
- ✅ **Can access** all regular app features
- ❌ **No admin privileges**

## 🔍 **Admin Page Features**

When you access the Admin Dashboard, you'll find:

### **📊 User Analytics Tab:**
- **User Activity Tracking**
- **Page Visit Statistics**
- **User Interaction Metrics**
- **Session Duration Analysis**
- **Browser and Device Analytics**

### **🎫 Support Management Tab:**
- **View All Support Tickets**
- **Respond to User Inquiries**
- **Ticket Status Management**
- **Support Analytics**

### **👥 User Management Tab:**
- **View All Registered Users**
- **User Account Information**
- **User Activity Logs**
- **Account Status Management**

### **⚙️ System Settings Tab:**
- **Application Configuration**
- **System Health Monitoring**
- **Database Management**
- **Export/Import Functions**

## 🔧 **Technical Details**

### **How Admin Detection Works:**
```python
# In app.py line 1216:
is_admin = AdminAuth.is_admin_user(st.session_state.get('username', ''))

# In admin_system.py:
def is_admin_user(username: str) -> bool:
    # Checks if user has 'admin' role in config.yaml
    roles = user_data.get('roles', [])
    return 'admin' in roles if roles else False
```

### **Admin Page Addition:**
```python
# In app.py lines 1227-1229:
if is_admin:
    pages.append(st.Page(use_admin_dashboard, title="🔧 Admin Dashboard", icon="⚙️"))
```

## 🛡️ **Security Features**

### **Access Control:**
- ✅ **Role-based access** - Only users with `admin` role can access
- ✅ **Session-based** - Must be logged in to see admin features
- ✅ **Dynamic navigation** - Admin page only appears for admin users
- ✅ **Permission checks** - Additional checks within admin dashboard

### **Admin Account Configuration:**
```yaml
# In config.yaml:
credentials:
  usernames:
    admin:
      email: <EMAIL>
      name: Admin User
      password: $2b$12$... # Encrypted password
      roles:
      - admin  # This makes the user an admin
```

## 🚨 **Troubleshooting**

### **"I don't see the Admin Dashboard"**
**Possible causes:**
1. **Not logged in as admin** - Make sure you're using the `admin` username
2. **Wrong password** - Verify your admin password is correct
3. **Missing admin role** - Check config.yaml has `roles: [admin]` for admin user
4. **Cache issues** - Try refreshing the page or clearing browser cache

### **"Access Denied: Admin privileges required"**
**This means:**
- You reached the admin page URL but don't have admin permissions
- The system detected you're not an admin user
- Log out and log back in with the admin account

### **"Admin Dashboard not working"**
**Check:**
1. **Config.yaml format** - Make sure roles field is properly formatted
2. **Authentication status** - Ensure you're properly logged in
3. **Browser console** - Check for any JavaScript errors
4. **App restart** - Try restarting the Streamlit app

## 📋 **Quick Access Checklist**

### **To Access Admin Page:**
- [ ] Start app: `streamlit run app.py`
- [ ] Login with username: `admin`
- [ ] Enter correct admin password
- [ ] Look for "🔧 Admin Dashboard" in navigation
- [ ] Click on Admin Dashboard
- [ ] Verify you can see admin features

### **If Admin Page Missing:**
- [ ] Confirm you're logged in as `admin` (not other users)
- [ ] Check config.yaml has `roles: [admin]` for admin user
- [ ] Refresh the page
- [ ] Try logging out and back in
- [ ] Restart the Streamlit app

## 🎯 **Summary**

**The admin page is located in the main navigation menu, but it's only visible when you're logged in as the `admin` user.**

**Steps to access:**
1. **Login as `admin`**
2. **Look for "🔧 Admin Dashboard"** in the navigation
3. **Click to access** all admin features

**The admin page provides comprehensive system administration tools including user analytics, support ticket management, user management, and system settings.**

**If you don't see the admin page, make sure you're logged in with the correct admin credentials!** 🔧
