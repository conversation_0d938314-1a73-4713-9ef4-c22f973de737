#!/usr/bin/env python3
"""
Test Job Search Feature
=======================

Test script to verify the job search functionality works correctly.
"""

import asyncio
import sys
import os

def test_job_crawler():
    """Test job crawler functionality"""
    print("🧪 Testing Job Crawler...")
    
    try:
        from job_crawler import VietnamJobCrawler, search_jobs, get_supported_job_sites
        
        print("✅ Job crawler imported successfully")
        
        # Test supported sites
        sites = get_supported_job_sites()
        print(f"✅ Supported job sites: {', '.join(sites)}")
        
        # Test crawler initialization
        crawler = VietnamJobCrawler()
        print("✅ Job crawler initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ Job crawler test failed: {e}")
        return False

def test_ai_matcher():
    """Test AI matcher functionality"""
    print("\n🧪 Testing AI Matcher...")
    
    try:
        from job_ai_matcher import JobAIMatcher, check_dependencies
        
        print("✅ AI matcher imported successfully")
        
        # Check dependencies
        deps_ok = check_dependencies()
        if deps_ok:
            print("✅ All AI dependencies available")
        else:
            print("⚠️ Some AI dependencies missing (this is OK for basic testing)")
        
        # Test matcher initialization
        matcher = JobAIMatcher()
        print("✅ AI matcher initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ AI matcher test failed: {e}")
        return False

def test_job_search_interface():
    """Test job search interface"""
    print("\n🧪 Testing Job Search Interface...")
    
    try:
        from job_search_interface import create_job_search_interface
        
        print("✅ Job search interface imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Job search interface test failed: {e}")
        return False

async def test_actual_job_search():
    """Test actual job search functionality"""
    print("\n🧪 Testing Actual Job Search...")
    
    try:
        from job_crawler import search_jobs
        
        print("🔍 Searching for 'python developer' jobs...")
        
        # Test with minimal parameters
        jobs = await search_jobs(
            keyword="python developer",
            location="ho chi minh",
            max_pages_per_site=1  # Just 1 page for testing
        )
        
        print(f"✅ Search completed! Found {len(jobs)} jobs")
        
        if jobs:
            # Show first job as example
            first_job = jobs[0]
            print(f"📋 Example job:")
            print(f"   Title: {first_job.get('title', 'N/A')}")
            print(f"   Company: {first_job.get('company', 'N/A')}")
            print(f"   Location: {first_job.get('location', 'N/A')}")
            print(f"   Source: {first_job.get('source', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Actual job search test failed: {e}")
        return False

def test_cv_processing():
    """Test CV processing functionality"""
    print("\n🧪 Testing CV Processing...")
    
    try:
        from job_ai_matcher import JobAIMatcher
        
        matcher = JobAIMatcher()
        
        # Test with sample CV text
        sample_cv = """
        John Doe
        Software Developer
        
        Experience:
        - 3 years as Python Developer at Tech Company
        - 2 years as Junior Developer at Startup
        
        Skills:
        - Python, Django, Flask
        - JavaScript, React
        - SQL, PostgreSQL
        - Git, Docker
        
        Education:
        - Bachelor of Computer Science
        """
        
        if matcher.model:
            print("🤖 Testing AI CV analysis...")
            analysis = matcher.analyze_cv_with_ai(sample_cv)
            
            if "error" not in analysis:
                print("✅ CV analysis successful")
                print(f"   Skills detected: {len(analysis.get('skills', []))}")
                print(f"   Experience: {analysis.get('experience_years', 'Unknown')}")
            else:
                print(f"⚠️ CV analysis failed: {analysis['error']}")
        else:
            print("⚠️ Google AI not configured - skipping CV analysis test")
        
        return True
        
    except Exception as e:
        print(f"❌ CV processing test failed: {e}")
        return False

def test_app_integration():
    """Test integration with main app"""
    print("\n🧪 Testing App Integration...")
    
    try:
        # Test that app can import job search
        import app
        
        # Check if job search function exists
        if hasattr(app, 'use_crawl_jobs'):
            print("✅ Job search function available in main app")
        else:
            print("❌ Job search function missing in main app")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ App integration test failed: {e}")
        return False

def check_dependencies():
    """Check if all dependencies are installed"""
    print("🔍 Checking Dependencies...")
    
    dependencies = {
        'crawl4ai': 'Job crawling',
        'google.generativeai': 'Google AI (optional)',
        'PyPDF2': 'PDF processing (optional)',
        'sentence_transformers': 'Embeddings (optional)',
        'beautifulsoup4': 'HTML parsing',
        'requests': 'HTTP requests',
        'pandas': 'Data processing',
        'plotly': 'Visualization'
    }
    
    missing = []
    available = []
    
    for dep, description in dependencies.items():
        try:
            __import__(dep)
            available.append(f"✅ {dep} - {description}")
        except ImportError:
            missing.append(f"❌ {dep} - {description}")
    
    print("\nAvailable dependencies:")
    for dep in available:
        print(f"  {dep}")
    
    if missing:
        print("\nMissing dependencies:")
        for dep in missing:
            print(f"  {dep}")
        print(f"\n💡 Install missing dependencies with:")
        print(f"   pip install -r job_requirements.txt")
    
    return len(missing) == 0

async def main():
    """Run all tests"""
    print("🚀 Testing Job Search Feature")
    print("=" * 50)
    
    # Check dependencies first
    deps_ok = check_dependencies()
    
    # Run tests
    tests = [
        test_job_crawler(),
        test_ai_matcher(),
        test_job_search_interface(),
        test_app_integration()
    ]
    
    # Test CV processing if AI available
    tests.append(test_cv_processing())
    
    # Test actual search if basic dependencies available
    if deps_ok:
        tests.append(await test_actual_job_search())
    else:
        print("\n⚠️ Skipping actual job search test due to missing dependencies")
    
    print("\n" + "=" * 50)
    print("🎉 Job Search Feature Testing Complete!")
    
    passed = sum(tests)
    total = len(tests)
    
    if passed == total:
        print(f"\n✅ All {total} tests passed!")
        print("\n🚀 Job Search Feature is ready to use!")
        print("\n📋 What's available:")
        print("  ✅ Job crawling from Vietnamese sites")
        print("  ✅ AI-powered job matching (with Google AI key)")
        print("  ✅ CV upload and analysis")
        print("  ✅ Job market analytics")
        print("  ✅ Export functionality")
        
        print("\n🔧 Next steps:")
        print("  1. Get Google AI API key for full AI features")
        print("  2. Run: streamlit run app.py")
        print("  3. Navigate to Job Search tab")
        print("  4. Start searching for jobs!")
        
    else:
        print(f"\n⚠️ {passed}/{total} tests passed")
        print("\nSome features may not work correctly.")
        print("Check the error messages above and install missing dependencies.")

if __name__ == "__main__":
    asyncio.run(main())
