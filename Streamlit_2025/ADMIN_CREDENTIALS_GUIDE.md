# 🔐 Admin Credentials Management Guide

## 📋 Current Admin Credentials

After running the credential update tool, your current admin login is:

- **Username:** `admin`
- **Password:** `admin@123`
- **Role:** `admin` (full access to all features)

## 🛠️ How to Change Admin Credentials

### Method 1: Using the Credential Update Tool (Recommended)

1. **Run the update script:**
   ```bash
   python update_admin_credentials.py
   ```

2. **Choose from available options:**
   - **Option 1:** Update existing admin password
   - **Option 2:** Create new admin user
   - **Option 3:** Change admin username
   - **Option 4:** Update admin details (name, email)

3. **Follow the interactive prompts**

### Method 2: Manual Config.yaml Editing

If you prefer to edit manually, you need to:

1. **Generate a password hash:**
   ```python
   import bcrypt
   password = "your_new_password"
   hashed = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())
   print(hashed.decode('utf-8'))
   ```

2. **Update config.yaml:**
   ```yaml
   credentials:
     usernames:
       your_admin_username:
         email: <EMAIL>
         first_name: Your
         last_name: Name
         password: $2b$12$your_hashed_password_here
         roles:
           - admin
   ```

## 🔑 Admin System Features

### What Admin Users Can Access:

#### 📊 **Analytics Dashboard**
- **User Metrics:** Total users, sessions, average duration
- **Daily Active Users:** Trend charts and statistics
- **Browser Analytics:** User browser distribution
- **Page Popularity:** Most visited pages
- **Activity Patterns:** Hourly usage patterns
- **User Rankings:** Most active users

#### 👥 **User Management**
- **User Overview:** Total, admin, and regular users
- **User Details:** Names, emails, roles, login status
- **Failed Login Tracking:** Security monitoring
- **Role Management:** Admin vs regular user roles

#### 🎫 **Support Ticket Management**
- **Ticket Overview:** Open, closed, in-progress tickets
- **Ticket Details:** User messages, timestamps, priorities
- **Status Management:** Update ticket status
- **Response System:** Reply to user inquiries

#### 💬 **Live Chat Support**
- **Real-time Chat:** Direct communication with users
- **Chat History:** Full conversation logs
- **Multi-ticket Support:** Handle multiple conversations
- **Admin Responses:** Professional support interface

#### ⚙️ **System Settings**
- **Data Export:** Analytics and ticket data export
- **Database Management:** System information
- **Backup Options:** Data backup and restore
- **System Monitoring:** Performance metrics

### What Regular Users Can Access:

#### 🔧 **All App Features:**
- General Search (AI-powered and basic)
- Stock Analysis with recommendations
- News Crawling (enhanced multi-source)
- Review Analysis with sentiment analysis

#### 💬 **Support Chat Widget:**
- **Floating Chat Button:** Available on all pages
- **Quick Actions:** Bug reports, questions, feature requests
- **Ticket Creation:** Automatic support ticket generation
- **Real-time Chat:** Direct communication with admin

## 🚀 Getting Started with Admin Features

### Step 1: Login as Admin
1. Go to your Streamlit app
2. Use the login credentials:
   - Username: `admin`
   - Password: `admin@123`

### Step 2: Access Admin Dashboard
1. After login, you'll see "🔧 Admin Dashboard" in the navigation menu
2. Click on it to access admin features

### Step 3: Explore Admin Features
1. **Analytics Tab:** View user behavior and engagement
2. **User Management Tab:** Manage user accounts
3. **Support Tickets Tab:** Handle user support requests
4. **Live Chat Tab:** Respond to user inquiries
5. **System Settings Tab:** Export data and manage system

## 🔒 Security Best Practices

### Password Security:
- **Use Strong Passwords:** At least 8 characters with mixed case, numbers, symbols
- **Regular Updates:** Change admin passwords periodically
- **Unique Passwords:** Don't reuse passwords from other systems

### Access Control:
- **Limit Admin Users:** Only give admin access to trusted personnel
- **Monitor Activity:** Regularly check user analytics for suspicious activity
- **Backup Configs:** Keep backups of config.yaml before changes

### Data Protection:
- **Regular Backups:** Export analytics data regularly
- **Secure Storage:** Keep database files secure
- **Access Logs:** Monitor who accesses admin features

## 🛡️ User Support Best Practices

### Responding to Support Tickets:
1. **Quick Response:** Aim to respond within 24 hours
2. **Professional Tone:** Use friendly, helpful language
3. **Clear Solutions:** Provide step-by-step instructions
4. **Follow Up:** Check if issues are resolved

### Managing User Inquiries:
1. **Categorize Issues:** Bug reports, questions, feature requests
2. **Priority Levels:** Handle urgent issues first
3. **Knowledge Base:** Document common solutions
4. **Escalation:** Know when to involve technical team

## 📊 Understanding Analytics

### Key Metrics to Monitor:
- **Daily Active Users:** Growth trends
- **Session Duration:** User engagement levels
- **Page Popularity:** Most used features
- **Browser Distribution:** Technical compatibility
- **Support Tickets:** User satisfaction indicators

### Using Analytics for Improvements:
- **Feature Usage:** Focus development on popular features
- **User Behavior:** Optimize user experience based on patterns
- **Performance Issues:** Identify and fix bottlenecks
- **Support Trends:** Improve documentation for common issues

## 🔧 Troubleshooting

### Common Issues:

#### Can't Access Admin Dashboard:
- **Check Role:** Ensure user has 'admin' role in config.yaml
- **Verify Login:** Confirm correct username/password
- **Clear Cache:** Refresh browser and clear Streamlit cache

#### Password Not Working:
- **Use Update Tool:** Run `python update_admin_credentials.py`
- **Check Hash:** Ensure password is properly hashed in config.yaml
- **Backup Restore:** Use backup config if needed

#### Database Issues:
- **Check Permissions:** Ensure write access to database file
- **Restart App:** Sometimes requires Streamlit restart
- **Recreate Database:** Delete user_analytics.db to recreate

## 📞 Support

If you need help with admin features:

1. **Check this guide first**
2. **Use the credential update tool for password issues**
3. **Check the test scripts for troubleshooting**
4. **Review the admin system code for technical details**

## 🎯 Summary

Your admin system provides:
- ✅ **Secure Authentication:** Role-based access control
- ✅ **User Analytics:** Comprehensive behavior tracking
- ✅ **Support System:** Ticket management and live chat
- ✅ **Easy Management:** User-friendly admin interface
- ✅ **Data Export:** Professional reporting capabilities

**Current Admin Login:**
- Username: `admin`
- Password: `admin@123`

**Remember to change the default password for security!**
