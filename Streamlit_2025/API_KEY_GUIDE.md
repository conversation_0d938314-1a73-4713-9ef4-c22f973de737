# 🔑 Google AI API Key Setup - Visual Guide

## ✅ **Good News: Dependencies Fixed!**

All missing dependencies have been installed:
- ✅ PyPDF2 (for PDF CV processing)
- ✅ python-docx (for Word document processing)
- ✅ google-generativeai (for AI features)

## 🎯 **Where to Input the API Key**

### **Step 1: Start the App**
```bash
streamlit run app.py
```

### **Step 2: Navigate to Job Search**
1. In the app sidebar, click **"Job Search"**
2. You'll see 4 tabs at the top
3. Click the **"🤖 AI Job Matching"** tab

### **Step 3: You'll See the API Key Setup Section**
The app will show:
```
🔑 Google AI Setup
🤖 To use AI job matching, you need a FREE Google AI API key

📋 How to get your FREE Google AI API key (click to expand)
[Detailed instructions will be shown]

🔐 Enter Your API Key:
[Text input field] [🧪 Test API Key button]
```

## 🔗 **Get Your FREE API Key**

### **Quick Link:**
👉 **https://makersuite.google.com/app/apikey**

### **What You'll See:**
1. **Google AI Studio page** opens
2. **Sign in** with any Google account (Gmail)
3. **Blue "Create API Key" button** - click it
4. **Choose "Create API key in new project"**
5. **Copy the generated key** (starts with `AIzaSy...`)

## 📝 **How to Enter the API Key**

### **In the App:**
1. **Paste the key** in the text input field
2. **Click "🧪 Test API Key"** button
3. **Wait for "✅ API key is valid and working!"** message
4. **You'll see balloons** 🎉 when it works!

### **Example API Key Format:**
```
AIzaSyC1234567890abcdefghijklmnopqrstuvwxyz
```
(Your actual key will be different)

## 🎉 **What Happens After Setup**

### **With API Key (Full Features):**
- ✅ Upload CV for AI analysis
- ✅ Get AI-powered job matching
- ✅ Receive match scores (0-100%)
- ✅ Get personalized application advice
- ✅ Advanced skill matching

### **Without API Key (Basic Features):**
- ✅ Search jobs across 4 Vietnamese sites
- ✅ Filter and sort results
- ✅ View job analytics
- ✅ Export job data
- ❌ No AI matching or CV analysis

## 🔧 **Troubleshooting**

### **"API key test failed":**
- Make sure you copied the **complete** key
- Check for extra spaces at the beginning/end
- Try generating a new key if needed

### **"Google AI not available":**
- Make sure you installed: `pip install google-generativeai`
- Restart the Streamlit app after installation

### **Can't find the API key input:**
- Make sure you're in **Job Search** → **AI Job Matching** tab
- The input field is under "🔐 Enter Your API Key:"

## 💡 **Pro Tips**

### **API Key Security:**
- ✅ The key is stored securely in your session
- ✅ It's not logged or saved permanently
- ✅ You can change it anytime using "🔄 Change API Key"

### **Usage Limits:**
- ✅ **FREE tier:** 60 requests per minute
- ✅ **Generous limits:** Enough for normal job searching
- ✅ **No credit card required**

### **Best Practices:**
- ✅ Test the key immediately after entering
- ✅ Keep your key private (don't share it)
- ✅ You can always generate a new one if needed

## 🚀 **Ready to Use!**

Once you enter the API key:

1. **Upload your CV** (PDF, Word, or text)
2. **Search for jobs** in the Search tab
3. **Get AI-powered matches** with scores and advice
4. **Find your perfect job** faster with AI!

## 📞 **Still Need Help?**

### **In the App:**
- Click the **"💬 Need Help?"** button (bottom-right corner)
- Create a support ticket for assistance

### **Quick Test:**
- Try the **basic job search first** (no API key needed)
- Search for "developer" or "marketing" to see it working
- Then add the API key for AI features

---

## 🎯 **Summary**

**Where to input API key:**
Job Search → AI Job Matching tab → "🔐 Enter Your API Key" section

**Get API key:**
https://makersuite.google.com/app/apikey (FREE!)

**Test it works:**
Click "🧪 Test API Key" button

**You're all set!** 🚀
