#!/usr/bin/env python3
"""
MySQL Connection Troubleshooting Tool
=====================================

This script helps diagnose and fix MySQL connection issues.
"""

import os
import sys
import subprocess
import platform
import socket
import time

def check_mysql_installation():
    """Check if MySQL is installed and running"""
    print("🔍 Checking MySQL Installation...")
    
    system = platform.system().lower()
    
    if system == "darwin":  # macOS
        # Check if MySQL is installed via Homebrew
        try:
            result = subprocess.run(['brew', 'services', 'list'], 
                                  capture_output=True, text=True, timeout=10)
            if 'mysql' in result.stdout:
                if 'started' in result.stdout:
                    print("✅ MySQL is installed and running (Homebrew)")
                    return True
                else:
                    print("⚠️ MySQL is installed but not running (Homebrew)")
                    print("💡 Try: brew services start mysql")
                    return False
        except:
            pass
        
        # Check if MySQL is installed via MySQL installer
        mysql_paths = [
            '/usr/local/mysql/bin/mysql',
            '/opt/homebrew/bin/mysql',
            '/usr/local/bin/mysql'
        ]
        
        for path in mysql_paths:
            if os.path.exists(path):
                print(f"✅ MySQL found at: {path}")
                return True
        
        print("❌ MySQL not found on macOS")
        print("💡 Install MySQL:")
        print("   Option 1: brew install mysql")
        print("   Option 2: Download from https://dev.mysql.com/downloads/mysql/")
        return False
    
    elif system == "linux":
        # Check if MySQL service is running
        try:
            result = subprocess.run(['systemctl', 'is-active', 'mysql'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✅ MySQL service is running (systemctl)")
                return True
            else:
                print("⚠️ MySQL service is not running")
                print("💡 Try: sudo systemctl start mysql")
                return False
        except:
            pass
        
        # Check if mysqld is running
        try:
            result = subprocess.run(['pgrep', 'mysqld'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✅ MySQL daemon is running")
                return True
        except:
            pass
        
        print("❌ MySQL not running on Linux")
        print("💡 Install MySQL:")
        print("   Ubuntu/Debian: sudo apt install mysql-server")
        print("   CentOS/RHEL: sudo yum install mysql-server")
        return False
    
    elif system == "windows":
        # Check Windows services
        try:
            result = subprocess.run(['sc', 'query', 'MySQL'], 
                                  capture_output=True, text=True, timeout=5)
            if 'RUNNING' in result.stdout:
                print("✅ MySQL service is running (Windows)")
                return True
            else:
                print("⚠️ MySQL service is not running")
                print("💡 Try: net start MySQL")
                return False
        except:
            pass
        
        print("❌ MySQL not found on Windows")
        print("💡 Install MySQL from: https://dev.mysql.com/downloads/installer/")
        return False
    
    return False

def check_port_availability(host='localhost', port=3306):
    """Check if MySQL port is accessible"""
    print(f"🔍 Checking port {port} on {host}...")
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ Port {port} is open on {host}")
            return True
        else:
            print(f"❌ Port {port} is not accessible on {host}")
            print("💡 Possible issues:")
            print("   - MySQL is not running")
            print("   - Firewall blocking the port")
            print("   - MySQL configured on different port")
            return False
    except Exception as e:
        print(f"❌ Error checking port: {e}")
        return False

def check_python_mysql_connector():
    """Check if mysql-connector-python is installed"""
    print("🔍 Checking Python MySQL connector...")
    
    try:
        import mysql.connector
        print("✅ mysql-connector-python is installed")
        print(f"   Version: {mysql.connector.__version__}")
        return True
    except ImportError:
        print("❌ mysql-connector-python is not installed")
        print("💡 Install with: pip install mysql-connector-python")
        return False

def test_basic_connection(host='localhost', port=3306, user='root', password=''):
    """Test basic MySQL connection"""
    print(f"🔍 Testing connection to {host}:{port} as {user}...")
    
    try:
        import mysql.connector
        from mysql.connector import Error
        
        connection = mysql.connector.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            connection_timeout=10
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ Connection successful!")
            print(f"   MySQL version: {version[0]}")
            cursor.close()
            connection.close()
            return True
        else:
            print("❌ Connection failed - not connected")
            return False
            
    except Error as e:
        print(f"❌ MySQL Error: {e}")
        
        # Provide specific error guidance
        error_str = str(e).lower()
        if "access denied" in error_str:
            print("💡 Access denied - check username/password")
        elif "can't connect" in error_str or "connection refused" in error_str:
            print("💡 Connection refused - check if MySQL is running")
        elif "unknown host" in error_str:
            print("💡 Unknown host - check hostname/IP address")
        elif "timeout" in error_str:
            print("💡 Connection timeout - check network/firewall")
        
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def get_mysql_default_credentials():
    """Get common default MySQL credentials to try"""
    print("🔍 Trying common default credentials...")
    
    common_configs = [
        {'user': 'root', 'password': ''},
        {'user': 'root', 'password': 'root'},
        {'user': 'root', 'password': 'password'},
        {'user': 'root', 'password': 'mysql'},
        {'user': 'mysql', 'password': ''},
        {'user': 'admin', 'password': 'admin'},
    ]
    
    for config in common_configs:
        print(f"   Trying: {config['user']} / {'(empty)' if not config['password'] else '***'}")
        if test_basic_connection(user=config['user'], password=config['password']):
            print(f"✅ Found working credentials: {config['user']}")
            return config
        time.sleep(1)
    
    print("❌ No default credentials worked")
    return None

def create_mysql_user_guide():
    """Provide guide for creating MySQL user"""
    print("\n📋 MySQL User Setup Guide:")
    print("=" * 40)
    
    print("\n1. Connect to MySQL as root:")
    print("   mysql -u root -p")
    
    print("\n2. Create a new user for Streamlit:")
    print("   CREATE USER 'streamlit_user'@'localhost' IDENTIFIED BY 'your_password';")
    
    print("\n3. Grant necessary privileges:")
    print("   GRANT ALL PRIVILEGES ON streamlit_analytics.* TO 'streamlit_user'@'localhost';")
    print("   FLUSH PRIVILEGES;")
    
    print("\n4. Create the database:")
    print("   CREATE DATABASE streamlit_analytics CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;")
    
    print("\n5. Test the connection:")
    print("   mysql -u streamlit_user -p streamlit_analytics")

def reset_mysql_root_password():
    """Guide for resetting MySQL root password"""
    print("\n🔐 MySQL Root Password Reset Guide:")
    print("=" * 40)
    
    system = platform.system().lower()
    
    if system == "darwin":  # macOS
        print("\nFor macOS (Homebrew MySQL):")
        print("1. Stop MySQL: brew services stop mysql")
        print("2. Start in safe mode: mysqld_safe --skip-grant-tables &")
        print("3. Connect: mysql -u root")
        print("4. Reset password:")
        print("   USE mysql;")
        print("   UPDATE user SET authentication_string=PASSWORD('new_password') WHERE User='root';")
        print("   FLUSH PRIVILEGES;")
        print("5. Restart MySQL: brew services restart mysql")
        
    elif system == "linux":
        print("\nFor Linux:")
        print("1. Stop MySQL: sudo systemctl stop mysql")
        print("2. Start in safe mode: sudo mysqld_safe --skip-grant-tables &")
        print("3. Connect: mysql -u root")
        print("4. Reset password:")
        print("   USE mysql;")
        print("   UPDATE user SET authentication_string=PASSWORD('new_password') WHERE User='root';")
        print("   FLUSH PRIVILEGES;")
        print("5. Restart MySQL: sudo systemctl start mysql")
    
    elif system == "windows":
        print("\nFor Windows:")
        print("1. Stop MySQL service")
        print("2. Create init file with: ALTER USER 'root'@'localhost' IDENTIFIED BY 'new_password';")
        print("3. Start MySQL with: mysqld --init-file=C:\\path\\to\\init-file.txt")
        print("4. Restart MySQL service normally")

def main():
    """Main troubleshooting function"""
    print("🔧 MySQL Connection Troubleshooting Tool")
    print("=" * 50)
    
    # Step 1: Check MySQL installation
    mysql_installed = check_mysql_installation()
    
    # Step 2: Check Python connector
    connector_installed = check_python_mysql_connector()
    
    if not connector_installed:
        print("\n❌ Cannot proceed without mysql-connector-python")
        print("Run: pip install mysql-connector-python")
        return
    
    # Step 3: Check port availability
    port_open = check_port_availability()
    
    if not mysql_installed or not port_open:
        print("\n❌ MySQL is not properly installed or running")
        print("Please install and start MySQL first")
        return
    
    # Step 4: Try default credentials
    working_config = get_mysql_default_credentials()
    
    if working_config:
        print(f"\n✅ Working MySQL credentials found!")
        print(f"   Username: {working_config['user']}")
        print(f"   Password: {'(empty)' if not working_config['password'] else working_config['password']}")
        
        # Test database creation
        try:
            import mysql.connector
            connection = mysql.connector.connect(
                host='localhost',
                port=3306,
                user=working_config['user'],
                password=working_config['password']
            )
            cursor = connection.cursor()
            cursor.execute("CREATE DATABASE IF NOT EXISTS streamlit_analytics CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✅ Database 'streamlit_analytics' created/verified")
            connection.close()
            
            # Create .env file
            env_content = f"""MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=streamlit_analytics
MYSQL_USER={working_config['user']}
MYSQL_PASSWORD={working_config['password']}
"""
            with open('.env', 'w') as f:
                f.write(env_content)
            print("✅ .env file created with working credentials")
            
        except Exception as e:
            print(f"⚠️ Could not create database: {e}")
    
    else:
        print("\n❌ No working credentials found")
        print("\nTroubleshooting options:")
        print("1. Reset MySQL root password")
        print("2. Create new MySQL user")
        print("3. Check MySQL configuration")
        
        choice = input("\nShow password reset guide? (y/n): ").strip().lower()
        if choice == 'y':
            reset_mysql_root_password()
        
        choice = input("\nShow user creation guide? (y/n): ").strip().lower()
        if choice == 'y':
            create_mysql_user_guide()

if __name__ == "__main__":
    main()
