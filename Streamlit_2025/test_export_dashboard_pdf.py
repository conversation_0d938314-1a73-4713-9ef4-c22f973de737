# import streamlit as st
# import pandas as pd
# import numpy as np
# import plotly.graph_objects as go
# import plotly.express as px
# import matplotlib.pyplot as plt
# from streamlit_echarts import st_echarts

# # Thiết lập trang
# st.set_page_config(layout="wide", page_title="Đánh giá Onboarding")

# # CSS tùy chỉnh
# custom_css = """
# <style>
#     /* Thiết lập chung */
#     .main {
#         background-color: #f5f5f5;
#         padding: 1rem;
#     }
    
#     /* Các thẻ thông tin */
#     .card {
#         border-radius: 10px;
#         padding: 1rem;
#         margin-bottom: 1rem;
#         display: flex;
#         flex-direction: column;
#         position: relative;
#     }
    
#     .card-green {
#         background-color: #e6f5f0;
#         border-left: 5px solid #4cc9a4;
#     }
    
#     .card-red {
#         background-color: #ffeae9;
#         border-left: 5px solid #ff5c55;
#     }
    
#     .status-icon {
#         position: absolute;
#         top: 10px;
#         right: 10px;
#         font-size: 24px;
#         width: 32px;
#         height: 32px;
#         display: flex;
#         align-items: center;
#         justify-content: center;
#         border-radius: 50%;
#     }
    
#     .status-icon.success {
#         background-color: #4cc9a4;
#         color: white;
#     }
    
#     .status-icon.failure {
#         background-color: #ff5c55;
#         color: white;
#     }
    
#     .card-title {
#         font-size: 1rem;
#         font-weight: bold;
#         color: #333;
#         margin-bottom: 0.5rem;
#     }
    
#     .card-value {
#         font-size: 2rem;
#         font-weight: bold;
#     }
    
#     .green-value {
#         color: #4cc9a4;
#     }
    
#     .red-value {
#         color: #ff5c55;
#     }
    
#     /* Thanh dữ liệu chất lượng */
#     .data-quality-bar {
#         background-color: #4cc9a4;
#         border-radius: 5px;
#         padding: 0.5rem;
#         color: white;
#         font-weight: bold;
#         margin-bottom: 1rem;
#     }
    
#     /* Các thẻ thông tin phụ */
#     .mini-card {
#         background-color: #e6f5f0;
#         border-radius: 5px;
#         padding: 0.7rem;
#         margin-bottom: 1rem;
#     }
    
#     .mini-title {
#         font-size: 0.9rem;
#         font-weight: bold;
#         color: #333;
#     }
    
#     .mini-value {
#         font-weight: bold;
#         color: #333;
#     }
    
#     /* Đồ thị và biểu đồ */
#     .chart-container {
#         background-color: white;
#         border-radius: 10px;
#         padding: 1rem;
#         box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
#     }
    
#     /* Chú thích */
#     .legend-item {
#         display: flex;
#         align-items: center;
#         margin-right: 1rem;
#     }
    
#     .legend-color {
#         width: 20px;
#         height: 20px;
#         margin-right: 5px;
#         border-radius: 3px;
#     }
    
#     .legend-container {
#         display: flex;
#         flex-wrap: wrap;
#         margin-top: 1rem;
#     }
# </style>
# """

# st.markdown(custom_css, unsafe_allow_html=True)

# # Tiêu đề chính
# st.markdown("<h1 style='font-size: 24px; color: #333;'>Onboarding Assessment:</h1>", unsafe_allow_html=True)

# # Tên (đã ẩn)
# st.markdown("<h2 style='font-size: 28px; color: #00ccaa;'>NAME REMOVED</h2>", unsafe_allow_html=True)

# # Hàng đầu với 4 thẻ thông tin
# col1, col2, col3, col4 = st.columns(4)

# with col1:
#     st.markdown("""
#     <div class="card card-green">
#         <div class="status-icon success">✓</div>
#         <div class="card-title">Credit Score</div>
#         <div class="card-value green-value">686</div>
#     </div>
#     """, unsafe_allow_html=True)

# with col2:
#     st.markdown("""
#     <div class="card card-red">
#         <div class="status-icon failure">✕</div>
#         <div class="card-title">Trading</div>
#         <div class="card-value red-value">11 mths</div>
#     </div>
#     """, unsafe_allow_html=True)

# with col3:
#     st.markdown("""
#     <div class="card card-red">
#         <div class="status-icon failure">✕</div>
#         <div class="card-title">Industry</div>
#         <div style="color: #ff5c55; font-size: 1.2rem;">Construction Services</div>
#     </div>
#     """, unsafe_allow_html=True)

# with col4:
#     st.markdown("""
#     <div class="card card-red">
#         <div class="status-icon failure">✕</div>
#         <div class="card-title">A.I. Recommendation</div>
#         <div class="card-value red-value">Decline</div>
#         <div style="background-color: #ff5c55; height: 8px; width: 80%; margin-top: 10px; border-radius: 4px;"></div>
#     </div>
#     """, unsafe_allow_html=True)

# # Thanh dữ liệu chất lượng
# st.markdown("""
# <div class="data-quality-bar">
#     Data Quality
# </div>
# """, unsafe_allow_html=True)

# # Hàng các thẻ thông tin phụ
# mini_cols = st.columns(6)

# mini_data = [
#     {"title": "Credit Enquires (12mths)", "value": "2"},
#     {"title": "Director Dereg/Struck", "value": "0"},
#     {"title": "Director Adverse", "value": "0"},
#     {"title": "Total Past Due", "value": "$0"},
#     {"title": "Owing 61 to 90", "value": "$0"},
#     {"title": "Owing 90+", "value": "$0"},
# ]

# for i, col in enumerate(mini_cols):
#     with col:
#         st.markdown(f"""
#         <div class="mini-card">
#             <div class="mini-title">{mini_data[i]['title']}</div>
#             <div class="mini-value">{mini_data[i]['value']}</div>
#         </div>
#         """, unsafe_allow_html=True)

# # Tiêu đề phần liên quan
# st.markdown("<h2 style='font-size: 22px; color: #333; margin-top: 20px;'>Related Entities</h2>", unsafe_allow_html=True)

# # Phần biểu đồ
# col_graph1, col_graph2, col_graph3 = st.columns([1, 1, 1])

# # Biểu đồ mối quan hệ
# with col_graph1:
#     # Giả lập sơ đồ mối quan hệ
#     st.markdown("""
#     <div class="chart-container" style="min-height: 300px;">
#         <div style="text-align: center; position: relative; height: 250px;">
#             <!-- Node chính -->
#             <div style="position: absolute; top: 100px; left: 50%; transform: translateX(-50%); 
#                  background-color: #e0e0e0; width: 100px; height: 50px; border-radius: 25px; 
#                  display: flex; align-items: center; justify-content: center; font-weight: bold;">
#                 NAME REMOVED
#             </div>
            
#             <!-- Node trên -->
#             <div style="position: absolute; top: 20px; left: 50%; transform: translateX(-50%);
#                  background-color: #ff5c55; width: 120px; height: 50px; border-radius: 25px;
#                  display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
#                 NAME REMOVED
#             </div>
            
#             <!-- Đường kết nối -->
#             <div style="position: absolute; top: 70px; left: 50%; transform: translateX(-50%);
#                  width: 2px; height: 30px; background-color: #999;"></div>
            
#             <!-- Node dưới -->
#             <div style="position: absolute; top: 180px; left: 50%; transform: translateX(-50%);
#                  background-color: #ff5c55; width: 120px; height: 50px; border-radius: 25px;
#                  display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
#                 NAME REMOVED
#             </div>
            
#             <!-- Đường kết nối -->
#             <div style="position: absolute; top: 150px; left: 50%; transform: translateX(-50%);
#                  width: 2px; height: 30px; background-color: #999;"></div>
#         </div>
#     </div>
#     """, unsafe_allow_html=True)

# # Biểu đồ Ownership
# with col_graph2:
#     ownership_options = {
#         "tooltip": {"trigger": "item"},
#         "series": [
#             {
#                 "name": "Ownership",
#                 "type": "pie",
#                 "radius": ["50%", "70%"],
#                 "avoidLabelOverlap": False,
#                 "itemStyle": {
#                     "borderRadius": 10,
#                     "borderColor": "#fff",
#                     "borderWidth": 2
#                 },
#                 "label": {"show": False},
#                 "emphasis": {
#                     "label": {"show": True, "fontSize": "18", "fontWeight": "bold"}
#                 },
#                 "labelLine": {"show": False},
#                 "data": [
#                     {"value": 100, "name": "Unknown", "itemStyle": {"color": "#4cc9a4"}}
#                 ]
#             }
#         ]
#     }
    
#     with st.container():
#         st.markdown('<div class="chart-container">', unsafe_allow_html=True)
#         st.markdown('<div style="text-align: center; padding: 10px;">Ownership</div>', unsafe_allow_html=True)
#         st_echarts(options=ownership_options, height="250px")
#         st.markdown('</div>', unsafe_allow_html=True)

# # Biểu đồ Score
# with col_graph3:
#     # Tạo dữ liệu cho biểu đồ đồng hồ đo
#     def gauge_chart():
#         fig = go.Figure(go.Indicator(
#             mode="gauge+number+delta",
#             value=686,
#             delta={"reference": 500, "valueformat": "", "suffix": "", "font": {"size": 16}},
#             gauge={
#                 "axis": {"range": [0, 900], "tickvals": [200, 400, 600, 800], "tickwidth": 1},
#                 "bar": {"color": "#4cc9a4"},
#                 "bgcolor": "white",
#                 "borderwidth": 2,
#                 "bordercolor": "gray",
#                 "steps": [
#                     {"range": [0, 400], "color": "rgba(255, 92, 85, 0.8)"},
#                     {"range": [400, 600], "color": "rgba(255, 178, 45, 0.8)"},
#                     {"range": [600, 900], "color": "rgba(76, 201, 164, 0.3)"}
#                 ],
#                 "threshold": {
#                     "line": {"color": "green", "width": 4},
#                     "thickness": 0.75,
#                     "value": 686
#                 }
#             },
#             title={
#                 "text": "Score",
#                 "font": {"size": 24}
#             },
#             domain={"x": [0, 1], "y": [0, 1]}
#         ))

#         fig.update_layout(
#             height=250,
#             margin=dict(l=20, r=20, t=50, b=20),
#             paper_bgcolor="rgba(0,0,0,0)",
#             font={"color": "#333", "family": "Arial"}
#         )
        
#         return fig

#     with st.container():
#         st.markdown('<div class="chart-container">', unsafe_allow_html=True)
#         st.plotly_chart(gauge_chart(), use_container_width=True)
#         st.markdown('</div>', unsafe_allow_html=True)

# # Tiêu đề phần Risk Factors
# st.markdown("<h2 style='font-size: 22px; color: #333; margin-top: 20px; text-align: right;'>Risk Factors</h2>", unsafe_allow_html=True)

# # Biểu đồ radar Risk Factors
# def radar_chart():
#     # Dữ liệu cho biểu đồ
#     categories = ['Enquires', 'Time in Business', 'Industry', 'Credit Score', 
#                  'Director Adverse', 'Trade Payments', 'Online Presence']
    
#     # Giá trị thực tế và giá trị tối đa
#     values = [80, 30, 25, 85, 90, 70, 75]
#     max_values = [100, 100, 100, 100, 100, 100, 100]
    
#     # Tạo dữ liệu biểu đồ
#     fig = go.Figure()
    
#     # Thêm vùng đỏ (Rủi ro cao)
#     fig.add_trace(go.Scatterpolar(
#         r=[40] * len(categories),
#         theta=categories,
#         fill='toself',
#         name='High Risk',
#         fillcolor='rgba(255, 92, 85, 0.2)',
#         line=dict(color='rgba(255, 92, 85, 0)')
#     ))
    
#     # Thêm vùng thực tế
#     fig.add_trace(go.Scatterpolar(
#         r=values,
#         theta=categories,
#         fill='toself',
#         name='Actual',
#         fillcolor='rgba(76, 201, 164, 0.5)',
#         line=dict(color='#4cc9a4', width=2)
#     ))
    
#     # Cấu hình layout
#     fig.update_layout(
#         polar=dict(
#             radialaxis=dict(
#                 visible=True,
#                 range=[0, 100]
#             )
#         ),
#         showlegend=False,
#         height=300,
#         margin=dict(l=40, r=40, t=20, b=20),
#         paper_bgcolor="rgba(0,0,0,0)",
#         font=dict(color="#333")
#     )
    
#     return fig

# st.plotly_chart(radar_chart(), use_container_width=True)

# # Phần chú thích
# st.markdown("""
# <div class="legend-container">
#     <div class="legend-item">
#         <div class="legend-color" style="background-color: #4cc9a4;"></div>
#         <span>Approved</span>
#     </div>
#     <div class="legend-item">
#         <div class="legend-color" style="background-color: #ff5c55;"></div>
#         <span>Declined</span>
#     </div>
#     <div class="legend-item">
#         <div class="legend-color" style="background-color: #ffb22d;"></div>
#         <span>Pending</span>
#     </div>
#     <div class="legend-item">
#         <div class="legend-color" style="background-color: #ff8a8a;"></div>
#         <span>Suspended</span>
#     </div>
# </div>
# """, unsafe_allow_html=True)




# import streamlit as st
# import pandas as pd
# import numpy as np


# st.markdown("""
# <style>
# /* TOTAL */
# div[data-testid="stVerticalBlockBorderWrapper"]:nth-of-type(2) > div:nth-child(1) > div[data-testid="stVerticalBlock"] > div[data-testid="stHorizontalBlock"] > div[data-testid="stColumn"]:nth-of-type(1) {
#     background-color: #f0f2f6; /* Màu xám nhạt */
#     padding: 15px;             /* Thêm khoảng đệm bên trong */
#     border-radius: 10px;       /* Bo tròn góc */
#     margin-bottom: 15px;       /* Khoảng cách với container dưới */
#     border: 1px solid #cccccc; /* Viền nhẹ */
# }
# /* Sub1 */
# div[data-testid="stVerticalBlockBorderWrapper"]:nth-of-type(2) > div:nth-child(1) > div[data-testid="stVerticalBlock"] > div[data-testid="stHorizontalBlock"] > div[data-testid="stColumn"]:nth-of-type(1) > div[data-testid="stVerticalBlockBorderWrapper"] > div:nth-child(1) > div[data-testid="stVerticalBlock"] > div[data-testid="stHorizontalBlock"] > div[data-testid="stColumn"]:nth-of-type(1){
#     background-color: #fff000; /* Màu xám nhạt */
#     padding: 15px;             /* Thêm khoảng đệm bên trong */
#     border-radius: 10px;       /* Bo tròn góc */
#     margin-bottom: 15px;       /* Khoảng cách với container dưới */
#     border: 1px solid #cccccc; /* Viền nhẹ */
# }
# /* Sub2 */
# div[data-testid="stVerticalBlockBorderWrapper"]:nth-of-type(2) > div:nth-child(1) > div[data-testid="stVerticalBlock"] > div[data-testid="stHorizontalBlock"] > div[data-testid="stColumn"]:nth-of-type(1) > div[data-testid="stVerticalBlockBorderWrapper"] > div:nth-child(1) > div[data-testid="stVerticalBlock"] > div[data-testid="stHorizontalBlock"] > div[data-testid="stColumn"]:nth-of-type(2){
#     background-color: #000fff; /* Màu xám nhạt */
#     padding: 15px;             /* Thêm khoảng đệm bên trong */
#     border-radius: 10px;       /* Bo tròn góc */
#     margin-bottom: 15px;       /* Khoảng cách với container dưới */
#     border: 1px solid #cccccc; /* Viền nhẹ */
# }
            
# div[data-testid="stVerticalBlockBorderWrapper"]:nth-of-type(2) > div:nth-child(1) > div[data-testid="stVerticalBlock"] > div[data-testid="stHorizontalBlock"] > div[data-testid="stColumn"]:nth-of-type(2) {
#     background-color: #00ff00; /* Màu xám nhạt */
#     padding: 15px;             /* Thêm khoảng đệm bên trong */
#     border-radius: 10px;       /* Bo tròn góc */
#     margin-bottom: 15px;       /* Khoảng cách với container dưới */
#     border: 1px solid #cccccc; /* Viền nhẹ */
# }

# /* Lớp cho filter1_chart1_container */
# .filter-chart1-bg {
#     background-color: #e7f3ff; /* Màu xanh dương nhạt */
#     padding: 15px;
#     border-radius: 10px;
#     margin-bottom: 15px;
#     border: 1px solid #b3d7ff;
# }

# /* Lớp cho scorecard_chart2_chart3_container */
# div[data-testid="stVerticalBlockBorderWrapper"]:nth-of-type(4) > div:nth-child(1) > div[data-testid="stVerticalBlock"] > div[data-testid="stHorizontalBlock"] > div[data-testid="stColumn"]:nth-of-type(1) {
#     background-color: #ff0000; /* Màu xám nhạt */
#     padding: 15px;             /* Thêm khoảng đệm bên trong */
#     border-radius: 10px;       /* Bo tròn góc */
#     margin-bottom: 15px;       /* Khoảng cách với container dưới */
#     border: 1px solid #cccccc; /* Viền nhẹ */
# }

# div[data-testid="stVerticalBlockBorderWrapper"]:nth-of-type(4) > div:nth-child(1) > div[data-testid="stVerticalBlock"] > div[data-testid="stHorizontalBlock"] > div[data-testid="stColumn"]:nth-of-type(2) {
#     background-color: #00ff00; /* Màu xám nhạt */
#     padding: 15px;             /* Thêm khoảng đệm bên trong */
#     border-radius: 20px;       /* Bo tròn góc */
#     margin-bottom: 15px;       /* Khoảng cách với container dưới */
#     border: 1px solid #cccccc; /* Viền nhẹ */
# }

# div[data-testid="stVerticalBlockBorderWrapper"]:nth-of-type(4) > div:nth-child(1) > div[data-testid="stVerticalBlock"] > div[data-testid="stHorizontalBlock"] > div[data-testid="stColumn"]:nth-of-type(3) {
#     background-color: #0000ff; /* Màu xám nhạt */
#     padding: 15px;             /* Thêm khoảng đệm bên trong */
#     border-radius: 30px;       /* Bo tròn góc */
#     margin-bottom: 15px;       /* Khoảng cách với container dưới */
#     border: 1px solid #cccccc; /* Viền nhẹ */
# }

# /* (Tùy chọn) Loại bỏ khoảng trống mặc định Streamlit thêm vào giữa các block con */
# /* Điều này giúp div bọc sát hơn với nội dung nếu cần */
# /*
# [data-testid="stVerticalBlock"] > [style*="flex-direction: column;"] > [data-testid="stVerticalBlock"] {
#     gap: 0.5rem; /* Giảm khoảng cách mặc định nếu muốn */
# }
# */

# </style>
# """, unsafe_allow_html=True)

# # 2. Tạo các container
# info_container = st.container()
# filter1_chart1_container = st.container()
# scorecard_chart2_chart3_container = st.container()

# # --- Container Thông tin ---
# with info_container:
#     col1, col2 = st.columns(2)
#     with col1:
#         # Bọc nội dung bằng div với class tương ứng
#         st.subheader("ℹ️ Thông tin chung")
#         st.write("Đây là khu vực hiển thị thông tin tổng quan hoặc mô tả.")
#         cols = st.columns(2)
#         with cols[0]:
#             st.metric("Số liệu A", "1,234", "10%")
#         with cols[1]:
#             st.metric("Số liệu B", "98%", "-2%")
#         # Đóng thẻ div bọc
#         st.markdown('</div>', unsafe_allow_html=True)
#     with col2:
#         # Bọc nội dung bằng div với class tương ứng
#         st.subheader("ℹ️ Thông tin chung 2")
#         st.write("Đây là khu vực hiển thị thông tin tổng quan hoặc mô tả.")
#         cols = st.columns(2)
#         with cols[0]:
#             st.metric("Số liệu A", "1,234", "10%")
#         with cols[1]:
#             st.metric("Số liệu B", "98%", "-2%")
#         # Đóng thẻ div bọc
#         st.markdown('</div>', unsafe_allow_html=True)

# # --- Container Filter và Chart 1 ---
# with filter1_chart1_container:
#     # Bọc nội dung bằng div với class tương ứng
#     st.markdown('<div class="filter-chart1-bg">', unsafe_allow_html=True)
#     st.subheader("📊 Bộ lọc và Biểu đồ 1")
#     option = st.selectbox('Chọn tùy chọn:', ['A', 'B', 'C'])
#     # Ví dụ biểu đồ
#     chart_data1 = pd.DataFrame(
#         np.random.randn(20, 3),
#         columns=['a', 'b', 'c'])
#     st.line_chart(chart_data1)
#     # Đóng thẻ div bọc
#     st.markdown('</div>', unsafe_allow_html=True)


# # --- Container Scorecard, Chart 2, Chart 3 ---
# with scorecard_chart2_chart3_container:
#     # Bọc nội dung bằng div với class tương ứng
#     st.markdown('<div class="scorecard-charts-bg">', unsafe_allow_html=True)
#     st.subheader("📈 Scorecards và Biểu đồ khác")
#     # Ví dụ bố cục cột
#     col1, col2, col3 = st.columns(3)
#     with col1:
#         st.metric("KPI 1", "150", delta="5", delta_color="inverse")
#         st.write("Chi tiết KPI 1...")
#     with col2:
#         st.metric("KPI 2", "85%", delta="-3%", delta_color="inverse")
#         chart_data2 = pd.DataFrame(np.random.rand(10, 2), columns=['X', 'Y'])
#         st.bar_chart(chart_data2)
#     with col3:
#         st.metric("KPI 3", "Active", delta="OK", delta_color="off")
#         chart_data3 = pd.DataFrame(np.random.randn(50, 1), columns=['Dữ liệu'])
#         st.area_chart(chart_data3)
#     # Đóng thẻ div bọc
#     st.markdown('</div>', unsafe_allow_html=True)

# # --- Nội dung khác bên ngoài các container được định dạng ---
# st.write("Nội dung bình thường không có nền đặc biệt.")





# import streamlit as st
# import pandas as pd
# import numpy as np
# import matplotlib.pyplot as plt
# import seaborn as sns
# import base64
# import io
# import os
# import tempfile
# import time
# from datetime import datetime
# import subprocess
# import sys
# import platform
# import webbrowser

# # Set page configuration
# st.set_page_config(
#     page_title="Analytics Dashboard",
#     page_icon="📊",
#     layout="wide"
# )

# # Function to generate sample data
# @st.cache_data
# def generate_data():
#     np.random.seed(42)
#     dates = pd.date_range(start='2024-01-01', periods=100, freq='D')
    
#     data = pd.DataFrame({
#         'Date': dates,
#         'Sales': np.random.normal(loc=1000, scale=200, size=100).cumsum(),
#         'Customers': np.random.normal(loc=100, scale=20, size=100).cumsum(),
#         'Conversion': np.random.uniform(0.1, 0.3, size=100),
#         'Region': np.random.choice(['North', 'South', 'East', 'West'], size=100)
#     })
    
#     return data

# # Function to download dataframe as CSV
# def download_csv(df):
#     csv = df.to_csv(index=False)
#     b64 = base64.b64encode(csv.encode()).decode()
#     filename = f"dashboard_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
#     href = f'<a href="data:file/csv;base64,{b64}" download="{filename}">Download CSV File</a>'
#     return href

# # Function to get binary file download link
# def get_binary_file_downloader_html(bin_file, file_label='File'):
#     with open(bin_file, 'rb') as f:
#         data = f.read()
#     bin_str = base64.b64encode(data).decode()
#     href = f'<a href="data:application/octet-stream;base64,{bin_str}" download="{os.path.basename(bin_file)}">{file_label}</a>'
#     return href

# # Function to download a figure
# def get_figure_download_link(fig, filename="plot.png"):
#     buf = io.BytesIO()
#     fig.savefig(buf, format='png', dpi=300, bbox_inches='tight')
#     buf.seek(0)
#     b64 = base64.b64encode(buf.getvalue()).decode()
#     href = f'<a href="data:image/png;base64,{b64}" download="{filename}">Download Plot</a>'
#     return href

# # Function to check if wkhtmltopdf is installed
# def check_wkhtmltopdf_installation():
#     try:
#         if platform.system() == "Windows":
#             # Check common Windows installation paths
#             possible_paths = [
#                 r'C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe',
#                 r'C:\Program Files (x86)\wkhtmltopdf\bin\wkhtmltopdf.exe'
#             ]
#             for path in possible_paths:
#                 if os.path.exists(path):
#                     return path
#             return None
#         else:
#             # For macOS and Linux, check if it's in PATH
#             result = subprocess.run(['which', 'wkhtmltopdf'], 
#                                    stdout=subprocess.PIPE, 
#                                    stderr=subprocess.PIPE)
#             if result.returncode == 0:
#                 return result.stdout.decode('utf-8').strip()
            
#             # Check common macOS paths (homebrew, macports)
#             mac_paths = [
#                 '/usr/local/bin/wkhtmltopdf',
#                 '/opt/homebrew/bin/wkhtmltopdf',
#                 '/opt/local/bin/wkhtmltopdf'
#             ]
#             for path in mac_paths:
#                 if os.path.exists(path):
#                     return path
#             return None
#     except Exception:
#         return None

# # Function to generate HTML export
# def export_html(filtered_data, metrics, regions, date_range):
#     # Create temporary directory for assets
#     temp_dir = tempfile.mkdtemp()
    
#     # Generate and save plots
#     time_series_path = os.path.join(temp_dir, "time_series.png")
#     fig_ts, ax = plt.subplots(figsize=(10, 6))
#     for metric in metrics:
#         if metric in ['Sales', 'Customers']:
#             ax.plot(filtered_data['Date'], filtered_data[metric], label=metric)
#     ax.set_title("Performance Over Time")
#     ax.set_xlabel("Date")
#     ax.set_ylabel("Value")
#     ax.grid(True, alpha=0.3)
#     ax.legend()
#     plt.savefig(time_series_path, dpi=300, bbox_inches='tight')
#     plt.close()
    
#     regional_path = os.path.join(temp_dir, "regional.png")
#     fig_reg, ax = plt.subplots(figsize=(10, 6))
#     sns.barplot(x='Region', y='Sales', data=filtered_data, ax=ax)
#     ax.set_title("Sales by Region")
#     ax.set_ylabel("Sales")
#     plt.savefig(regional_path, dpi=300, bbox_inches='tight')
#     plt.close()
    
#     # Get base64 encoded images
#     with open(time_series_path, "rb") as img_file:
#         time_series_b64 = base64.b64encode(img_file.read()).decode()
    
#     with open(regional_path, "rb") as img_file:
#         regional_b64 = base64.b64encode(img_file.read()).decode()
    
#     # Create HTML content with embedded images
#     html_content = f"""
#     <!DOCTYPE html>
#     <html>
#     <head>
#         <title>Dashboard Export</title>
#         <style>
#             body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
#             h1, h2 {{ color: #1E88E5; }}
#             table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
#             th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
#             th {{ background-color: #f2f2f2; }}
#             .metric-container {{ display: flex; flex-wrap: wrap; justify-content: space-between; margin-bottom: 20px; }}
#             .metric {{ flex: 0 0 30%; background-color: #f9f9f9; padding: 15px; 
#                       border: 1px solid #ddd; border-radius: 5px; margin-bottom: 10px; }}
#             .metric h3 {{ margin-top: 0; color: #333; }}
#             .value {{ font-size: 24px; font-weight: bold; color: #1E88E5; }}
#             .change {{ font-size: 14px; color: gray; }}
#             .positive {{ color: green; }}
#             .negative {{ color: red; }}
#             img {{ max-width: 100%; height: auto; margin: 10px 0; border: 1px solid #eee; }}
#             .chart-container {{ margin: 20px 0; }}
#             @media print {{
#                 .no-print {{ display: none; }}
#                 body {{ font-size: 12pt; }}
#                 h1 {{ font-size: 18pt; }}
#                 h2 {{ font-size: 16pt; }}
#             }}
#         </style>
#     </head>
#     <body>
#         <h1>Analytics Dashboard Export</h1>
#         <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        
#         <h2>Dashboard Parameters</h2>
#         <p><strong>Date Range:</strong> {date_range[0]} to {date_range[1]}</p>
#         <p><strong>Regions:</strong> {', '.join(regions)}</p>
#         <p><strong>Metrics:</strong> {', '.join(metrics)}</p>
        
#         <h2>Key Metrics</h2>
#         <div class="metric-container">
#             <div class="metric">
#                 <h3>Total Sales</h3>
#                 <div class="value">${filtered_data['Sales'].iloc[-1]:,.2f}</div>
#                 <div class="change {'positive' if filtered_data['Sales'].iloc[-1] - filtered_data['Sales'].iloc[0] > 0 else 'negative'}">
#                     {'+' if filtered_data['Sales'].iloc[-1] - filtered_data['Sales'].iloc[0] > 0 else ''}${filtered_data['Sales'].iloc[-1] - filtered_data['Sales'].iloc[0]:,.2f}
#                 </div>
#             </div>
#             <div class="metric">
#                 <h3>Total Customers</h3>
#                 <div class="value">{filtered_data['Customers'].iloc[-1]:,.0f}</div>
#                 <div class="change {'positive' if filtered_data['Customers'].iloc[-1] - filtered_data['Customers'].iloc[0] > 0 else 'negative'}">
#                     {'+' if filtered_data['Customers'].iloc[-1] - filtered_data['Customers'].iloc[0] > 0 else ''}{filtered_data['Customers'].iloc[-1] - filtered_data['Customers'].iloc[0]:,.0f}
#                 </div>
#             </div>
#             <div class="metric">
#                 <h3>Avg. Conversion Rate</h3>
#                 <div class="value">{filtered_data['Conversion'].mean():.2%}</div>
#                 <div class="change {'positive' if filtered_data['Conversion'].mean() - filtered_data['Conversion'].iloc[0] > 0 else 'negative'}">
#                     {'+' if filtered_data['Conversion'].mean() - filtered_data['Conversion'].iloc[0] > 0 else ''}{filtered_data['Conversion'].mean() - filtered_data['Conversion'].iloc[0]:.2%}
#                 </div>
#             </div>
#         </div>
        
#         <h2>Time Series Analysis</h2>
#         <div class="chart-container">
#             <img src="data:image/png;base64,{time_series_b64}" alt="Time Series Chart">
#         </div>
        
#         <h2>Regional Analysis</h2>
#         <div class="chart-container">
#             <img src="data:image/png;base64,{regional_b64}" alt="Regional Analysis">
#         </div>
        
#         <h2>Data Table</h2>
#         <table>
#             <tr>
#                 <th>Date</th>
#                 <th>Sales</th>
#                 <th>Customers</th>
#                 <th>Conversion</th>
#                 <th>Region</th>
#             </tr>
#     """
    
#     # Add table data
#     for _, row in filtered_data.head(20).iterrows():  # Limit to 20 rows for PDF readability
#         html_content += f"""
#             <tr>
#                 <td>{row['Date'].strftime('%Y-%m-%d')}</td>
#                 <td>${row['Sales']:,.2f}</td>
#                 <td>{row['Customers']:,.0f}</td>
#                 <td>{row['Conversion']:.2%}</td>
#                 <td>{row['Region']}</td>
#             </tr>
#         """
    
#     if len(filtered_data) > 20:
#         html_content += f"""
#             <tr>
#                 <td colspan="5" style="text-align:center">
#                     (Showing 20 of {len(filtered_data)} rows)
#                 </td>
#             </tr>
#         """
    
#     html_content += """
#         </table>
        
#         <div class="no-print">
#             <p>
#                 <strong>Print instructions:</strong> To save as PDF, use your browser's print function 
#                 (Ctrl+P or Cmd+P) and select "Save as PDF" as the destination.
#             </p>
#         </div>
        
#         <script>
#             // Auto-print for printing dialog
#             window.onload = function() {
#                 // Add button to print
#                 var printBtn = document.createElement("button");
#                 printBtn.innerHTML = "Print / Save as PDF";
#                 printBtn.style = "padding: 10px 20px; background: #1E88E5; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 20px;";
#                 printBtn.onclick = function() { window.print(); };
#                 document.body.appendChild(printBtn);
#             }
#         </script>
#     </body>
#     </html>
#     """
    
#     # Write HTML to file
#     html_path = os.path.join(temp_dir, f"dashboard_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html")
#     with open(html_path, 'w', encoding='utf-8') as html_file:
#         html_file.write(html_content)
    
#     return html_path, temp_dir

# # Function for PDF export if wkhtmltopdf is available
# def try_pdf_export(html_path, wkhtmltopdf_path):
#     try:
#         import pdfkit
        
#         # Configure path to wkhtmltopdf
#         config = pdfkit.configuration(wkhtmltopdf=wkhtmltopdf_path)
        
#         # Create PDF file
#         pdf_path = html_path.replace('.html', '.pdf')
        
#         # Set options for better rendering
#         options = {
#             'enable-local-file-access': None,
#             'quiet': None,
#             'print-media-type': None,
#             'no-outline': None,
#             'margin-top': '10mm',
#             'margin-right': '10mm',
#             'margin-bottom': '10mm',
#             'margin-left': '10mm',
#             'encoding': 'UTF-8',
#             'no-stop-slow-scripts': None
#         }
        
#         pdfkit.from_file(html_path, pdf_path, configuration=config, options=options)
#         return pdf_path
#     except Exception as e:
#         st.error(f"PDF export failed: {str(e)}")
#         return None

# # Main app
# def main():
#     # Check for wkhtmltopdf installation
#     wkhtmltopdf_path = check_wkhtmltopdf_installation()
    
#     # Title and description
#     st.title("📊 Analytics Dashboard")
#     st.markdown("### Interactive dashboard with export capabilities")
    
#     # Load data
#     data = generate_data()
    
#     # Dashboard settings sidebar
#     st.sidebar.header("Dashboard Settings")
    
#     # Date range filter
#     date_range = st.sidebar.date_input(
#         "Select Date Range",
#         value=(data['Date'].min().date(), data['Date'].max().date())
#     )
    
#     # Region filter
#     regions = st.sidebar.multiselect(
#         "Select Regions",
#         options=data['Region'].unique(),
#         default=data['Region'].unique()
#     )
    
#     # Metric selection
#     metrics = st.sidebar.multiselect(
#         "Select Metrics to Display",
#         options=['Sales', 'Customers', 'Conversion'],
#         default=['Sales', 'Customers']
#     )
    
#     # Apply filters
#     filtered_data = data[
#         (data['Date'] >= pd.Timestamp(date_range[0])) &
#         (data['Date'] <= pd.Timestamp(date_range[1])) &
#         (data['Region'].isin(regions))
#     ]
    
#     # Display metrics
#     st.header("Key Metrics")
#     col1, col2, col3 = st.columns(3)
#     with col1:
#         st.metric("Total Sales", f"${filtered_data['Sales'].iloc[-1]:,.2f}", 
#                   f"{filtered_data['Sales'].iloc[-1] - filtered_data['Sales'].iloc[0]:,.2f}")
#     with col2:
#         st.metric("Total Customers", f"{filtered_data['Customers'].iloc[-1]:,.0f}", 
#                   f"{filtered_data['Customers'].iloc[-1] - filtered_data['Customers'].iloc[0]:,.0f}")
#     with col3:
#         st.metric("Avg. Conversion Rate", f"{filtered_data['Conversion'].mean():.2%}", 
#                   f"{filtered_data['Conversion'].mean() - data['Conversion'].mean():.2%}")
    
#     # Create visualizations
#     st.header("Data Visualization")
    
#     tab1, tab2, tab3 = st.tabs(["Time Series", "Regional Analysis", "Data Table"])
    
#     with tab1:
#         # Time series chart
#         st.subheader("Time Series Analysis")
#         fig_ts, ax = plt.subplots(figsize=(10, 6))
        
#         for metric in metrics:
#             if metric in ['Sales', 'Customers']:
#                 ax.plot(filtered_data['Date'], filtered_data[metric], label=metric)
        
#         ax.set_title("Performance Over Time")
#         ax.set_xlabel("Date")
#         ax.set_ylabel("Value")
#         ax.grid(True, alpha=0.3)
#         ax.legend()
        
#         st.pyplot(fig_ts)
#         st.markdown(get_figure_download_link(fig_ts, "time_series.png"), unsafe_allow_html=True)
    
#     with tab2:
#         # Regional analysis
#         st.subheader("Regional Analysis")
        
#         fig_reg, ax = plt.subplots(figsize=(10, 6))
#         sns.barplot(x='Region', y='Sales', data=filtered_data, ax=ax)
#         ax.set_title("Sales by Region")
#         ax.set_ylabel("Sales")
        
#         st.pyplot(fig_reg)
#         st.markdown(get_figure_download_link(fig_reg, "regional_analysis.png"), unsafe_allow_html=True)
    
#     with tab3:
#         # Data table
#         st.subheader("Data Table")
#         st.dataframe(filtered_data)
#         st.markdown(download_csv(filtered_data), unsafe_allow_html=True)
    
#     # Export options
#     st.header("Export Dashboard")
    
#     # Export method selection
#     export_method = st.radio(
#         "Choose export method:",
#         ["Export as HTML", "Export as PDF"]
#     )
    
#     # Show wkhtmltopdf status
#     if export_method == "Export as PDF":
#         if wkhtmltopdf_path:
#             st.success(f"wkhtmltopdf detected at: {wkhtmltopdf_path}")
#         else:
#             st.warning("""
#             wkhtmltopdf not found. PDF export requires wkhtmltopdf to be installed.
            
#             On macOS, install it with: `brew install wkhtmltopdf`
            
#             Alternatively, use HTML export and print to PDF from your browser.
#             """)
    
#     export_button = st.button("Generate Export")
    
#     if export_button:
#         if export_method == "Export as HTML" or not wkhtmltopdf_path:
#             with st.spinner("Generating HTML export..."):
#                 html_path, temp_dir = export_html(filtered_data, metrics, regions, date_range)
#                 st.success("HTML export generated successfully!")
#                 st.markdown(get_binary_file_downloader_html(html_path, 'Download HTML Report'), unsafe_allow_html=True)
                
#                 # Button to open in browser
#                 if st.button("Open in Browser"):
#                     webbrowser.open('file://' + os.path.abspath(html_path))
                
#                 st.info("""
#                 **Tip:** After opening in browser, you can use the print function (Ctrl+P or Cmd+P) 
#                 and select "Save as PDF" to create a PDF version.
#                 """)
        
#         elif export_method == "Export as PDF" and wkhtmltopdf_path:
#             with st.spinner("Generating PDF export..."):
#                 html_path, temp_dir = export_html(filtered_data, metrics, regions, date_range)
#                 pdf_path = try_pdf_export(html_path, wkhtmltopdf_path)
                
#                 if pdf_path:
#                     st.success("PDF export generated successfully!")
#                     st.markdown(get_binary_file_downloader_html(pdf_path, 'Download PDF Report'), unsafe_allow_html=True)
#                 else:
#                     st.error("PDF generation failed. Please try HTML export instead and print to PDF from your browser.")
#                     st.markdown(get_binary_file_downloader_html(html_path, 'Download HTML Report'), unsafe_allow_html=True)

# if __name__ == "__main__":
#     main()



