from crawl4ai import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CacheMode, CrawlerRunConfig
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
import asyncio
import json
import streamlit as st
import requests
from bs4 import BeautifulSoup
import time
import random

async def crawl_google_results(keyword: str, pages: int = 3): # default page = 3
    """
    Crawl Google search results with improved error handling and no wait_for selector
    """
    # Simplified extraction strategy that doesn't rely on specific selectors
    search_schema = {
        "name": "SearchResults",
        "baseSelector": "div.g, div[data-ved], .g",  # Multiple fallback selectors
        "fields": [
            {"name": "title", "selector": "h3, .LC20lb", "type": "text"},
            {"name": "url", "selector": "a[href^='http'], a[href^='/url']", "type": "attribute", "attribute": "href"},
            {"name": "description", "selector": "div.VwiC3b, .s, span.st", "type": "text"}
        ]
    }

    extraction_strategy = JsonCssExtractionStrategy(search_schema)
    collected_data = []

    async with AsyncWebCrawler(
        headless=True,
        verbose=False,  # Reduce verbosity to avoid spam
        user_agent_mode="random"
    ) as crawler:

        for page in range(pages):
            try:
                start = page * 10  # Google paginates every 10 results
                search_url = f"https://www.google.com/search?q={keyword}&num=30&start={start}"

                # Simplified configuration without wait_for
                search_config = CrawlerRunConfig(
                    cache_mode=CacheMode.BYPASS,
                    extraction_strategy=extraction_strategy,
                    delay_before_return_html=2,  # Shorter delay
                    screenshot=False,
                    page_timeout=20000,  # Even shorter timeout - 20 seconds
                    # NO wait_for parameter at all
                    js_code="""
                        async () => {
                            // Simple wait for page load
                            await new Promise(r => setTimeout(r, 1500));

                            // Light scrolling to trigger any lazy loading
                            window.scrollTo(0, document.body.scrollHeight / 2);
                            await new Promise(r => setTimeout(r, 500));
                            window.scrollTo(0, 0);

                            // Final wait
                            await new Promise(r => setTimeout(r, 500));
                        }
                    """
                )

                search_result = await crawler.arun(url=search_url, config=search_config)

            except Exception as e:
                print(f"Warning: Error crawling page {page + 1}: {str(e)}")
                continue  # Skip this page and continue with next

            if search_result.success and search_result.extracted_content:
                try:
                    search_items = json.loads(search_result.extracted_content)
                except json.JSONDecodeError:
                    st.error("Error: Unable to parse search results JSON")
                    continue
                
                for item in search_items:
                    if "url" in item and item["url"].startswith("http"):
                        collected_data.append({
                            "title": item.get("title", ""),
                            "url": item["url"],
                            "description": item.get("description", "")
                        })
                        st.write(f"Extracted: {item['url']}")
            
            await asyncio.sleep(3)  # Avoid rate-limiting

    return collected_data

def simple_search_fallback(keyword: str, pages: int = 1):
    """
    Simple fallback search using requests and BeautifulSoup
    Used when crawl4ai fails
    """
    results = []

    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        for page in range(pages):
            start = page * 10
            url = f"https://www.google.com/search?q={keyword}&start={start}"

            try:
                response = requests.get(url, headers=headers, timeout=10)
                response.raise_for_status()

                soup = BeautifulSoup(response.content, 'html.parser')

                # Find search result containers
                search_results = soup.find_all('div', class_='g')

                for result in search_results:
                    try:
                        # Extract title
                        title_elem = result.find('h3')
                        title = title_elem.get_text() if title_elem else "No title"

                        # Extract URL
                        link_elem = result.find('a', href=True)
                        url = link_elem['href'] if link_elem else "No URL"

                        # Clean up Google redirect URLs
                        if url.startswith('/url?q='):
                            url = url.split('/url?q=')[1].split('&')[0]

                        # Extract description
                        desc_elem = result.find('div', class_='VwiC3b')
                        if not desc_elem:
                            desc_elem = result.find('span', class_='st')
                        description = desc_elem.get_text() if desc_elem else "No description"

                        if title != "No title" and url != "No URL":
                            results.append({
                                'title': title,
                                'url': url,
                                'description': description
                            })

                    except Exception as e:
                        continue  # Skip this result if parsing fails

                # Add delay between requests
                time.sleep(random.uniform(1, 2))

            except Exception as e:
                print(f"Error fetching page {page + 1}: {e}")
                continue

        return results

    except Exception as e:
        print(f"Fallback search failed: {e}")
        return []

async def robust_search(keyword: str, pages: int = 3):
    """
    Robust search that tries crawl4ai first, then falls back to simple search
    """
    try:
        # Try the main crawl4ai search first
        results = await crawl_google_results(keyword, pages)

        if results and len(results) > 0:
            return results
        else:
            print("Crawl4ai returned no results, trying fallback...")
            return simple_search_fallback(keyword, pages)

    except Exception as e:
        print(f"Crawl4ai failed: {e}, trying fallback...")
        return simple_search_fallback(keyword, pages)