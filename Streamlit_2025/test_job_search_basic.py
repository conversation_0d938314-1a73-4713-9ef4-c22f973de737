#!/usr/bin/env python3
"""
Test Basic Job Search (No AI)
=============================

Test that job search works without AI features.
"""

import asyncio
import sys

def test_basic_job_search():
    """Test basic job search functionality"""
    print("🧪 Testing Basic Job Search (No AI)...")
    
    try:
        # Test job crawler
        from job_crawler import search_jobs, get_supported_job_sites
        print("✅ Job crawler imported successfully")
        
        # Test supported sites
        sites = get_supported_job_sites()
        print(f"✅ Supported sites: {', '.join(sites)}")
        
        # Test simple search
        from simple_job_search import simple_job_search
        print("✅ Simple job search imported successfully")
        
        # Test with sample data
        jobs = simple_job_search("Data Analyst", "Hanoi")
        print(f"✅ Simple search returned {len(jobs)} jobs")
        
        if jobs:
            for i, job in enumerate(jobs[:2]):
                print(f"  {i+1}. {job.get('title', 'N/A')} at {job.get('company', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic job search test failed: {e}")
        return False

def test_job_interface_import():
    """Test job search interface import"""
    print("\n🧪 Testing Job Search Interface Import...")
    
    try:
        # This should work even without AI
        from job_search_interface import create_job_search_interface
        print("✅ Job search interface imported successfully")
        return True
        
    except Exception as e:
        print(f"❌ Job search interface import failed: {e}")
        print(f"Error details: {str(e)}")
        return False

async def test_actual_search():
    """Test actual job search"""
    print("\n🧪 Testing Actual Job Search...")
    
    try:
        from job_crawler import search_jobs
        
        # Test with minimal search
        jobs = await search_jobs("developer", "", "", 1)
        print(f"✅ Actual search completed, found {len(jobs)} jobs")
        
        if jobs:
            print("Sample job:")
            job = jobs[0]
            print(f"  Title: {job.get('title', 'N/A')}")
            print(f"  Company: {job.get('company', 'N/A')}")
            print(f"  Source: {job.get('source', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Actual search failed: {e}")
        return False

def main():
    """Run all basic tests"""
    print("🚀 Testing Job Search (Basic Mode - No AI)")
    print("=" * 50)
    
    tests = []
    
    # Test basic functionality
    tests.append(test_basic_job_search())
    
    # Test interface import
    tests.append(test_job_interface_import())
    
    # Test actual search
    try:
        tests.append(asyncio.run(test_actual_search()))
    except Exception as e:
        print(f"❌ Async test failed: {e}")
        tests.append(False)
    
    print("\n" + "=" * 50)
    print("🎉 Basic Job Search Testing Complete!")
    
    passed = sum(tests)
    total = len(tests)
    
    if passed == total:
        print(f"\n✅ All {total} tests passed!")
        print("\n🚀 Basic job search is working!")
        print("\n📋 What works:")
        print("  ✅ Job search without AI")
        print("  ✅ Simple search fallback")
        print("  ✅ Interface imports correctly")
        print("  ✅ Sample relevant jobs")
        
        print("\n🔧 To fix AI features:")
        print("  1. Get valid Google AI API key")
        print("  2. Make sure key has access to Gemini models")
        print("  3. Try different model names if needed")
        
    else:
        print(f"\n⚠️ {passed}/{total} tests passed")
        print("\nSome basic features may not work.")
        print("Check the error messages above.")
    
    print("\n🎯 Next steps:")
    print("  1. Run: streamlit run app.py")
    print("  2. Go to Job Search tab")
    print("  3. Try basic search (should work)")
    print("  4. Fix AI features if needed")

if __name__ == "__main__":
    main()
