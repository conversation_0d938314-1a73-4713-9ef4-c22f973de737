"""
AI-Powered Job Matching System
==============================

This module uses Google's free LLM API and RAG to match jobs with user CVs.
"""

import os
import json
import re
from typing import List, Dict, Optional, Tuple
import streamlit as st
import pandas as pd
from datetime import datetime
import hashlib

# Google AI imports
try:
    import google.generativeai as genai
    GOOGLE_AI_AVAILABLE = True
except ImportError:
    GOOGLE_AI_AVAILABLE = False
    st.warning("Google AI not available. Install with: pip install google-generativeai")

# Document processing imports
try:
    import PyPDF2
    from docx import Document
    DOCUMENT_PROCESSING_AVAILABLE = True
except ImportError:
    DOCUMENT_PROCESSING_AVAILABLE = False

# Vector embeddings for RAG
try:
    from sentence_transformers import SentenceTransformer
    import numpy as np
    from sklearn.metrics.pairwise import cosine_similarity
    EMBEDDINGS_AVAILABLE = True
except ImportError:
    EMBEDDINGS_AVAILABLE = False

class JobAIMatcher:
    """AI-powered job matching system using Google's free LLM and RAG"""
    
    def __init__(self):
        self.model = None
        self.embedding_model = None
        self.cv_embeddings = {}
        self.job_embeddings = {}
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize Google AI and embedding models"""
        
        # Initialize Google AI
        if GOOGLE_AI_AVAILABLE:
            api_key = os.getenv('GOOGLE_AI_API_KEY')
            if not api_key:
                # Try to get from Streamlit secrets (suppress error if not found)
                try:
                    if hasattr(st, 'secrets') and 'GOOGLE_AI_API_KEY' in st.secrets:
                        api_key = st.secrets['GOOGLE_AI_API_KEY']
                except Exception:
                    pass  # Ignore secrets errors
            
            if api_key:
                try:
                    genai.configure(api_key=api_key)

                    # Try different model names
                    model_names = [
                        'gemini-1.5-flash',  # Latest free model
                        'gemini-1.5-pro',    # Alternative
                        'gemini-pro',        # Legacy name
                        'models/gemini-1.5-flash',
                        'models/gemini-pro'
                    ]

                    for model_name in model_names:
                        try:
                            self.model = genai.GenerativeModel(model_name)
                            # Test the model works
                            test_response = self.model.generate_content("Test")
                            if test_response:
                                print(f"✅ Google AI initialized successfully with {model_name}")
                                break
                        except Exception:
                            continue

                    if not self.model:
                        print("Failed to initialize any Google AI model")

                except Exception as e:
                    print(f"Failed to initialize Google AI: {e}")
            else:
                print("⚠️ Google AI API key not found")
        
        # Initialize embedding model for RAG
        if EMBEDDINGS_AVAILABLE:
            try:
                self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
                print("✅ Embedding model initialized successfully")
            except Exception as e:
                print(f"Failed to initialize embedding model: {e}")
    
    def extract_text_from_file(self, uploaded_file) -> str:
        """Extract text from uploaded CV file"""

        if not DOCUMENT_PROCESSING_AVAILABLE:
            return "Error: Document processing not available. Please install PyPDF2 and python-docx."

        try:
            # Ensure we have a file name
            if not hasattr(uploaded_file, 'name') or not uploaded_file.name:
                return "Error: Invalid file uploaded"

            file_extension = uploaded_file.name.lower().split('.')[-1]

            if file_extension == 'pdf':
                result = self._extract_from_pdf(uploaded_file)
            elif file_extension in ['docx', 'doc']:
                result = self._extract_from_docx(uploaded_file)
            elif file_extension == 'txt':
                # Reset file pointer and read as bytes, then decode
                uploaded_file.seek(0)
                content = uploaded_file.read()
                if isinstance(content, bytes):
                    result = content.decode('utf-8')
                else:
                    result = str(content)
            else:
                return "Error: Unsupported file format. Please upload PDF, DOCX, or TXT files."

            # Ensure result is always a string
            if hasattr(result, 'text'):
                return str(result.text)
            elif not isinstance(result, str):
                return str(result)
            else:
                return result

        except Exception as e:
            return f"Error extracting text: {str(e)}"
    
    def _extract_from_pdf(self, uploaded_file) -> str:
        """Extract text from PDF file"""
        try:
            # Reset file pointer
            uploaded_file.seek(0)
            pdf_reader = PyPDF2.PdfReader(uploaded_file)
            text = ""
            for page in pdf_reader.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"

            if not text.strip():
                return "Error: Could not extract text from PDF. The PDF might be image-based."

            return text.strip()
        except Exception as e:
            return f"Error reading PDF: {str(e)}"

    def _extract_from_docx(self, uploaded_file) -> str:
        """Extract text from DOCX file"""
        try:
            # Reset file pointer
            uploaded_file.seek(0)
            doc = Document(uploaded_file)
            text = ""
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text += paragraph.text + "\n"

            if not text.strip():
                return "Error: Could not extract text from DOCX file."

            return text.strip()
        except Exception as e:
            return f"Error reading DOCX: {str(e)}"

    def _extract_text_from_response(self, response) -> str:
        """Safely extract text from Google AI response object"""
        try:
            # Handle direct text attribute
            if hasattr(response, 'text') and response.text:
                return response.text.strip()

            # Handle candidates structure
            elif hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                    parts_text = []
                    for part in candidate.content.parts:
                        if hasattr(part, 'text') and part.text:
                            parts_text.append(part.text)
                    return ''.join(parts_text).strip()
                else:
                    return str(candidate).strip()

            # Handle parts directly
            elif hasattr(response, 'parts') and response.parts:
                parts_text = []
                for part in response.parts:
                    if hasattr(part, 'text') and part.text:
                        parts_text.append(part.text)
                return ''.join(parts_text).strip()

            # Fallback to string conversion
            else:
                return str(response).strip()

        except Exception as e:
            print(f"Error extracting text from response: {e}")
            return str(response)

    def analyze_cv_with_ai(self, cv_text: str) -> Dict:
        """Analyze CV using Google AI to extract key information"""
        if not self.model:
            return {"error": "Google AI not available"}

        # Ensure cv_text is a string and handle Part objects FIRST
        if hasattr(cv_text, 'text'):
            cv_text = cv_text.text
        elif hasattr(cv_text, 'parts') and len(cv_text.parts) > 0:
            # Handle response with parts
            cv_text = ''.join([part.text for part in cv_text.parts if hasattr(part, 'text')])
        elif not isinstance(cv_text, str):
            cv_text = str(cv_text)

        # Ensure cv_text is a string before checking for errors
        if not isinstance(cv_text, str):
            cv_text = str(cv_text)

        # Now check for errors in the text (after ensuring it's a string)
        if cv_text and "error" in cv_text.lower():
            return {"error": cv_text}

        prompt = f"""
        Analyze the following CV and extract key information in JSON format:

        CV Text:
        {cv_text}
        
        Please extract and return a JSON object with the following fields:
        - skills: List of technical and soft skills
        - experience_years: Estimated years of experience
        - education: Education background
        - job_titles: Previous job titles/roles
        - industries: Industries worked in
        - languages: Programming languages or spoken languages
        - certifications: Any certifications mentioned
        - summary: Brief professional summary
        
        Return only valid JSON, no additional text.
        """
        
        try:
            response = self.model.generate_content(prompt)
            
            # Extract text from response safely
            response_text = self._extract_text_from_response(response)

            # Try to find JSON in the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                # Fallback: try to parse the entire response
                return json.loads(response_text)
                
        except Exception as e:
            print(f"Error analyzing CV with AI: {e}")
            return {"error": str(e)}
    
    def create_embeddings(self, text: str) -> Optional[np.ndarray]:
        """Create embeddings for text using sentence transformers"""
        
        if not self.embedding_model:
            return None
        
        try:
            embeddings = self.embedding_model.encode([text])
            return embeddings[0]
        except Exception as e:
            print(f"Error creating embeddings: {e}")
            return None
    
    def calculate_job_match_score(self, cv_analysis: Dict, job: Dict) -> Tuple[float, Dict]:
        """Calculate match score between CV and job using AI and embeddings"""
        
        if not self.model:
            return 0.0, {"error": "AI not available"}
        
        # Prepare job text for analysis
        job_text = f"""
        Job Title: {job.get('title', '')}
        Company: {job.get('company', '')}
        Location: {job.get('location', '')}
        Description: {job.get('description', '')}
        """
        
        # Create embeddings for similarity calculation
        cv_skills = " ".join(cv_analysis.get('skills', []))
        job_description = job.get('description', '')
        
        similarity_score = 0.0
        if self.embedding_model and cv_skills and job_description:
            try:
                cv_embedding = self.create_embeddings(cv_skills)
                job_embedding = self.create_embeddings(job_description)
                
                if cv_embedding is not None and job_embedding is not None:
                    similarity_score = cosine_similarity(
                        cv_embedding.reshape(1, -1),
                        job_embedding.reshape(1, -1)
                    )[0][0]
            except Exception as e:
                print(f"Error calculating similarity: {e}")
        
        # Use AI to analyze match
        prompt = f"""
        Analyze the match between this CV profile and job posting. Rate the match from 0-100 and provide reasoning.
        
        CV Profile:
        - Skills: {cv_analysis.get('skills', [])}
        - Experience: {cv_analysis.get('experience_years', 'Unknown')} years
        - Previous roles: {cv_analysis.get('job_titles', [])}
        - Industries: {cv_analysis.get('industries', [])}
        
        Job Posting:
        {job_text}
        
        Provide response in JSON format:
        {{
            "match_score": <0-100>,
            "reasoning": "<explanation>",
            "matching_skills": ["skill1", "skill2"],
            "missing_skills": ["skill1", "skill2"],
            "recommendations": "<advice for candidate>"
        }}
        
        Return only valid JSON.
        """
        
        try:
            response = self.model.generate_content(prompt)

            # Extract text from response safely
            response_text = self._extract_text_from_response(response)
            
            # Extract JSON from response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                ai_analysis = json.loads(json_str)
                
                # Combine AI score with embedding similarity
                ai_score = ai_analysis.get('match_score', 0) / 100.0
                combined_score = (ai_score * 0.7) + (similarity_score * 0.3)
                
                ai_analysis['combined_score'] = combined_score
                ai_analysis['similarity_score'] = similarity_score
                
                return combined_score, ai_analysis
            else:
                return similarity_score, {"error": "Could not parse AI response"}
                
        except Exception as e:
            print(f"Error in AI job matching: {e}")
            return similarity_score, {"error": str(e)}
    
    def rank_jobs_for_cv(self, cv_analysis: Dict, jobs: List[Dict]) -> List[Dict]:
        """Rank jobs based on CV match using AI and RAG"""
        
        ranked_jobs = []
        
        for job in jobs:
            try:
                match_score, match_analysis = self.calculate_job_match_score(cv_analysis, job)
                
                job_with_score = job.copy()
                job_with_score['match_score'] = match_score
                job_with_score['match_analysis'] = match_analysis
                
                ranked_jobs.append(job_with_score)
                
            except Exception as e:
                print(f"Error ranking job {job.get('title', 'Unknown')}: {e}")
                continue
        
        # Sort by match score (highest first)
        ranked_jobs.sort(key=lambda x: x.get('match_score', 0), reverse=True)
        
        return ranked_jobs
    
    def generate_application_advice(self, cv_analysis: Dict, job: Dict) -> str:
        """Generate personalized application advice using AI"""
        
        if not self.model:
            return "AI advice not available"
        
        prompt = f"""
        Generate personalized advice for applying to this job based on the candidate's CV.
        
        Candidate Profile:
        - Skills: {cv_analysis.get('skills', [])}
        - Experience: {cv_analysis.get('experience_years', 'Unknown')} years
        - Background: {cv_analysis.get('summary', '')}
        
        Job:
        - Title: {job.get('title', '')}
        - Company: {job.get('company', '')}
        - Description: {job.get('description', '')}
        
        Provide specific, actionable advice for:
        1. How to tailor the CV for this role
        2. Key points to highlight in cover letter
        3. Skills to emphasize
        4. Potential interview preparation tips
        
        Keep advice concise and practical.
        """
        
        try:
            response = self.model.generate_content(prompt)

            # Extract text from response safely
            return self._extract_text_from_response(response)
        except Exception as e:
            return f"Error generating advice: {str(e)}"

# Global AI matcher instance
ai_matcher = JobAIMatcher()

def setup_google_ai_key(tab_prefix=""):
    """Setup Google AI API key with improved UI"""
    st.subheader("🔑 Google AI Setup")

    # Check if API key is already configured
    current_key = os.getenv('GOOGLE_AI_API_KEY')
    if current_key and ai_matcher.model:
        st.success("✅ Google AI API key is configured and working!")

        # Option to change key
        if st.button("🔄 Change API Key", key=f"{tab_prefix}_change_api_key_button"):
            st.session_state.change_api_key = True

        if not st.session_state.get('change_api_key', False):
            return True

    # Show setup instructions
    st.info("🤖 To use AI job matching, you need a **FREE** Google AI API key")

    with st.expander("📋 How to get your FREE Google AI API key", expanded=True):
        st.markdown("""
        ### Step-by-step guide:

        1. **Go to Google AI Studio:**
           - Click this link: [Google AI Studio](https://makersuite.google.com/app/apikey)
           - Sign in with your Google account (Gmail)

        2. **Create API Key:**
           - Click "Create API Key" button
           - Choose "Create API key in new project" (recommended)
           - Copy the generated API key

        3. **Enter API Key Below:**
           - Paste the key in the input field below
           - Click "Test API Key" to verify it works

        **Note:** This is completely FREE with generous usage limits!
        """)

    # API key input
    st.markdown("### 🔐 Enter Your API Key:")

    col1, col2 = st.columns([3, 1])

    with col1:
        api_key = st.text_input(
            "Google AI API Key:",
            type="password",
            placeholder="Paste your API key here...",
            help="Get your free API key from Google AI Studio",
            key="google_ai_key_input"
        )

    with col2:
        test_button = st.button("🧪 Test API Key", type="primary", key=f"{tab_prefix}_test_api_key_button")

    # Test the API key
    if test_button and api_key:
        with st.spinner("Testing API key..."):
            try:
                # Configure and test the API key
                genai.configure(api_key=api_key)

                # Try different model names (Google has updated model names)
                model_names = [
                    'gemini-1.5-flash',  # Latest free model
                    'gemini-1.5-pro',    # Alternative
                    'gemini-pro',        # Legacy name
                ]

                test_model = None
                working_model_name = None
                last_error = None

                for model_name in model_names:
                    try:
                        test_model = genai.GenerativeModel(model_name)
                        # Test with a simple request
                        response = test_model.generate_content("Hello")

                        if response and (hasattr(response, 'text') or hasattr(response, 'candidates')):
                            working_model_name = model_name
                            break
                    except Exception as model_error:
                        last_error = str(model_error)
                        continue  # Try next model

                if test_model and working_model_name:
                    # Save the API key and working model
                    os.environ['GOOGLE_AI_API_KEY'] = api_key
                    ai_matcher.model = test_model

                    # Clear the change flag
                    if 'change_api_key' in st.session_state:
                        del st.session_state.change_api_key

                    st.success(f"✅ API key is valid and working!")
                    st.info(f"🤖 Using model: {working_model_name}")
                    st.balloons()
                    return True
                else:
                    st.error("❌ API key test failed - no working model found")
                    if last_error:
                        st.error(f"Last error: {last_error}")
                    st.info("💡 Troubleshooting steps:")
                    st.markdown("""
                    1. **Generate a new API key** from [Google AI Studio](https://makersuite.google.com/app/apikey)
                    2. **Make sure you're using the correct Google account**
                    3. **Check that your API key has access to Gemini models**
                    4. **Try copying the key again** (no extra spaces)
                    """)
                    return False

            except Exception as e:
                st.error(f"❌ API key test failed: {str(e)}")
                st.info("💡 Make sure you copied the complete API key from Google AI Studio")
                return False

    elif test_button and not api_key:
        st.error("❌ Please enter an API key first")

    # Show current status
    if not api_key:
        st.warning("⚠️ AI features will not work without an API key")
        st.info("💡 Don't worry - basic job search still works without AI!")

    return False

def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    
    if not GOOGLE_AI_AVAILABLE:
        missing_deps.append("google-generativeai")
    
    if not DOCUMENT_PROCESSING_AVAILABLE:
        missing_deps.append("PyPDF2 and python-docx")
    
    if not EMBEDDINGS_AVAILABLE:
        missing_deps.append("sentence-transformers and scikit-learn")
    
    if missing_deps:
        st.warning(f"Missing dependencies: {', '.join(missing_deps)}")
        st.info("Install with: pip install google-generativeai PyPDF2 python-docx sentence-transformers scikit-learn")
        return False
    
    return True
