#!/usr/bin/env python3
"""
Test script to verify search results persistence fix
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_session_state_management():
    """Test that session state variables are properly managed"""
    print("🧪 Testing Session State Management...")
    
    try:
        from enhanced_review_analysis import create_enhanced_review_analysis_interface
        
        # Test that the function exists
        print("✅ Enhanced review analysis interface imported successfully")
        
        # Test session state variables that should be initialized
        expected_session_vars = [
            "original_df",
            "analyzed_df", 
            "selected_app",
            "search_results",
            "last_search_query",
            "current_platform"
        ]
        
        print(f"✅ Expected session state variables: {len(expected_session_vars)}")
        for var in expected_session_vars:
            print(f"   - {var}")
        
        return True
        
    except Exception as e:
        print(f"❌ Session state management test failed: {str(e)}")
        return False

def test_search_flow_logic():
    """Test the search flow logic"""
    print("\n🧪 Testing Search Flow Logic...")
    
    try:
        # Test that we can import the search engine
        from enhanced_review_analysis import AppSearchEngine
        
        search_engine = AppSearchEngine()
        print("✅ AppSearchEngine initialized")
        
        # Test mock search workflow
        mock_search_results = [
            {
                'title': 'Test App 1',
                'appId': 'com.test.app1',
                'developer': 'Test Developer 1',
                'score': 4.5,
                'ratings': 1000,
                'category': 'Productivity',
                'free': True,
                'price': 0,
                'icon': 'https://example.com/icon1.png',
                'summary': 'A great test app'
            },
            {
                'title': 'Test App 2', 
                'appId': 'com.test.app2',
                'developer': 'Test Developer 2',
                'score': 4.2,
                'ratings': 500,
                'category': 'Entertainment',
                'free': False,
                'price': 2.99,
                'icon': 'https://example.com/icon2.png',
                'summary': 'Another test app'
            }
        ]
        
        print(f"✅ Mock search results created: {len(mock_search_results)} apps")
        
        # Test that search results have required fields
        required_fields = ['title', 'appId', 'developer', 'score', 'ratings', 'category', 'free', 'price']
        
        for i, app in enumerate(mock_search_results):
            missing_fields = [field for field in required_fields if field not in app]
            if missing_fields:
                print(f"❌ App {i+1} missing fields: {missing_fields}")
                return False
            else:
                print(f"✅ App {i+1} has all required fields")
        
        return True
        
    except Exception as e:
        print(f"❌ Search flow logic test failed: {str(e)}")
        return False

def test_button_key_uniqueness():
    """Test that all button keys are unique"""
    print("\n🧪 Testing Button Key Uniqueness...")
    
    try:
        # Read the enhanced review analysis file
        with open('enhanced_review_analysis.py', 'r') as f:
            content = f.read()
        
        # Find all button keys
        import re
        button_patterns = [
            r'st\.button\([^,]+,\s*key\s*=\s*["\']([^"\']+)["\']',
            r'st\.button\([^,]+,\s*[^,]+,\s*key\s*=\s*["\']([^"\']+)["\']'
        ]
        
        all_keys = []
        for pattern in button_patterns:
            keys = re.findall(pattern, content)
            all_keys.extend(keys)
        
        print(f"✅ Found {len(all_keys)} button keys")
        
        # Check for duplicates
        unique_keys = set(all_keys)
        if len(unique_keys) == len(all_keys):
            print("✅ All button keys are unique")
            for key in sorted(unique_keys):
                print(f"   - {key}")
            return True
        else:
            duplicates = [key for key in all_keys if all_keys.count(key) > 1]
            print(f"❌ Found duplicate keys: {set(duplicates)}")
            return False
        
    except Exception as e:
        print(f"❌ Button key uniqueness test failed: {str(e)}")
        return False

def test_search_persistence_fix():
    """Test the specific search persistence fix"""
    print("\n🧪 Testing Search Persistence Fix...")
    
    try:
        # Read the file and check for the fix
        with open('enhanced_review_analysis.py', 'r') as f:
            content = f.read()
        
        # Check that session state variables are properly initialized
        session_state_checks = [
            '"search_results" not in st.session_state',
            '"last_search_query" not in st.session_state', 
            '"current_platform" not in st.session_state'
        ]
        
        for check in session_state_checks:
            if check in content:
                print(f"✅ Found session state initialization: {check}")
            else:
                print(f"❌ Missing session state initialization: {check}")
                return False
        
        # Check that st.rerun() is not called after selecting an app
        if 'st.session_state.selected_app = app' in content:
            print("✅ Found app selection logic")
            
            # Check that there's no immediate st.rerun() after app selection
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'st.session_state.selected_app = app' in line:
                    # Check next few lines for actual st.rerun() calls (not comments)
                    next_lines = lines[i+1:i+5]
                    has_immediate_rerun = any('st.rerun()' in next_line and not next_line.strip().startswith('#') for next_line in next_lines)
                    if not has_immediate_rerun:
                        print("✅ No immediate st.rerun() after app selection")
                    else:
                        print("❌ Found immediate st.rerun() after app selection")
                        return False
        
        # Check for search results caching
        if 'st.session_state.search_results = search_results' in content:
            print("✅ Found search results caching logic")
        else:
            print("❌ Missing search results caching logic")
            return False
        
        # Check for "New Search" button
        if '"🔄 New Search"' in content:
            print("✅ Found 'New Search' button for clearing results")
        else:
            print("❌ Missing 'New Search' button")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Search persistence fix test failed: {str(e)}")
        return False

def test_import_and_syntax():
    """Test that the file can be imported without syntax errors"""
    print("\n🧪 Testing Import and Syntax...")
    
    try:
        import enhanced_review_analysis
        print("✅ Enhanced review analysis module imported successfully")
        
        # Test that main function exists
        if hasattr(enhanced_review_analysis, 'create_enhanced_review_analysis_interface'):
            print("✅ Main interface function exists")
        else:
            print("❌ Main interface function missing")
            return False
        
        # Test that classes exist
        required_classes = ['AppSearchEngine', 'ReviewAnalyticsDashboard']
        for class_name in required_classes:
            if hasattr(enhanced_review_analysis, class_name):
                print(f"✅ {class_name} class exists")
            else:
                print(f"❌ {class_name} class missing")
                return False
        
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error in enhanced_review_analysis.py: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Import test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Search Persistence Fix...\n")
    
    # Run tests
    session_ok = test_session_state_management()
    search_ok = test_search_flow_logic()
    keys_ok = test_button_key_uniqueness()
    persistence_ok = test_search_persistence_fix()
    import_ok = test_import_and_syntax()
    
    print("\n🎉 Search Persistence Fix Testing Completed!")
    
    if session_ok and search_ok and keys_ok and persistence_ok and import_ok:
        print("\n✅ All tests passed! The search persistence fix is working correctly.")
        print("\n📋 Summary of Fixes Applied:")
        print("✅ Added proper session state management for search results")
        print("✅ Removed immediate st.rerun() calls after app selection")
        print("✅ Added search results caching to preserve results")
        print("✅ Added 'New Search' button for clearing results")
        print("✅ Added unique keys for all buttons")
        print("✅ Improved user experience flow")
        
        print("\n🚀 How the fix works:")
        print("1. Search results are stored in session state")
        print("2. When you click 'Analyze Reviews', the page doesn't refresh")
        print("3. Search results remain visible while analysis proceeds")
        print("4. You can use 'New Search' to start over")
        print("5. 'Reset Analysis' clears everything")
        
        print("\n💡 User Experience Improvements:")
        print("- Search results persist after selecting an app")
        print("- No more disappearing search results")
        print("- Smooth transition from search to analysis")
        print("- Clear options to start new searches")
        
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
        
        if not session_ok:
            print("- Fix session state management")
        if not search_ok:
            print("- Fix search flow logic")
        if not keys_ok:
            print("- Fix button key conflicts")
        if not persistence_ok:
            print("- Fix search persistence implementation")
        if not import_ok:
            print("- Fix import and syntax errors")

if __name__ == "__main__":
    main()
