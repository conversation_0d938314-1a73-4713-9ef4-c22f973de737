#!/usr/bin/env python3
"""
Test Google AI Models
====================

Test script to check which Google AI models are available and working.
"""

import os
import sys

def test_google_ai_models():
    """Test different Google AI model names"""
    print("🧪 Testing Google AI Models...")
    
    try:
        import google.generativeai as genai
        print("✅ google-generativeai imported successfully")
    except ImportError:
        print("❌ google-generativeai not installed")
        print("Install with: pip install google-generativeai")
        return False
    
    # Get API key
    api_key = os.getenv('GOOGLE_AI_API_KEY')
    if not api_key:
        print("⚠️ No API key found in environment")
        print("Set with: export GOOGLE_AI_API_KEY='your_key_here'")
        
        # Try to get from user input
        api_key = input("Enter your Google AI API key (or press Enter to skip): ").strip()
        if not api_key:
            print("Skipping API key test")
            return False
    
    try:
        genai.configure(api_key=api_key)
        print("✅ API key configured")
    except Exception as e:
        print(f"❌ Failed to configure API key: {e}")
        return False
    
    # Test different model names
    model_names = [
        'gemini-1.5-flash',      # Latest free model
        'gemini-1.5-pro',        # Pro version
        'gemini-pro',            # Legacy name
        'models/gemini-1.5-flash', # With models/ prefix
        'models/gemini-1.5-pro',   # Pro with prefix
        'models/gemini-pro'      # Legacy with prefix
    ]
    
    working_models = []
    
    for model_name in model_names:
        try:
            print(f"🔍 Testing model: {model_name}")
            model = genai.GenerativeModel(model_name)
            
            # Test with a simple request
            response = model.generate_content("Say 'Hello'")
            
            if response and response.text:
                print(f"✅ {model_name} - WORKING")
                print(f"   Response: {response.text.strip()}")
                working_models.append(model_name)
            else:
                print(f"❌ {model_name} - No response")
                
        except Exception as e:
            print(f"❌ {model_name} - Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎉 Test Results:")
    
    if working_models:
        print(f"✅ Found {len(working_models)} working models:")
        for model in working_models:
            print(f"  - {model}")
        
        print(f"\n🚀 Recommended model: {working_models[0]}")
        return True
    else:
        print("❌ No working models found")
        print("\n💡 Troubleshooting:")
        print("1. Check your API key is valid")
        print("2. Make sure you have access to Google AI")
        print("3. Try generating a new API key")
        return False

def list_available_models():
    """List all available models from Google AI"""
    print("\n🔍 Listing Available Models...")
    
    try:
        import google.generativeai as genai
        
        api_key = os.getenv('GOOGLE_AI_API_KEY')
        if not api_key:
            print("⚠️ No API key available for listing models")
            return
        
        genai.configure(api_key=api_key)
        
        # List models
        models = genai.list_models()
        
        print("📋 Available models:")
        for model in models:
            print(f"  - {model.name}")
            if hasattr(model, 'supported_generation_methods'):
                methods = model.supported_generation_methods
                if 'generateContent' in methods:
                    print(f"    ✅ Supports generateContent")
                else:
                    print(f"    ❌ Does not support generateContent")
    
    except Exception as e:
        print(f"❌ Error listing models: {e}")

def main():
    """Main test function"""
    print("🚀 Google AI Model Testing")
    print("=" * 50)
    
    # Test models
    success = test_google_ai_models()
    
    # List available models if API key is available
    if os.getenv('GOOGLE_AI_API_KEY'):
        list_available_models()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Google AI is working!")
        print("Your job search AI features should now work correctly.")
    else:
        print("⚠️ Google AI test failed")
        print("Job search will work without AI features.")
    
    print("\n📋 Next steps:")
    print("1. Run: streamlit run app.py")
    print("2. Go to Job Search → AI Job Matching")
    print("3. Enter your API key and test it")

if __name__ == "__main__":
    main()
