import streamlit as st
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from vnstock import Vnstock
import datetime
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class StockPredictionModel:
    """
    Machine Learning model for predicting stock price movements
    """
    
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = []
        self.is_trained = False
        
    def create_technical_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Create technical analysis features for ML model
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with technical features
        """
        df = df.copy()
        
        # Price-based features
        df['price_change'] = df['close'].pct_change()
        df['high_low_ratio'] = df['high'] / df['low']
        df['close_open_ratio'] = df['close'] / df['open']
        
        # Moving averages
        for window in [5, 10, 20, 50]:
            df[f'ma_{window}'] = df['close'].rolling(window=window).mean()
            df[f'price_ma_{window}_ratio'] = df['close'] / df[f'ma_{window}']
        
        # Volatility features
        df['volatility_5'] = df['close'].rolling(window=5).std()
        df['volatility_20'] = df['close'].rolling(window=20).std()
        
        # Volume features
        df['volume_ma_5'] = df['volume'].rolling(window=5).mean()
        df['volume_ma_20'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio_5'] = df['volume'] / df['volume_ma_5']
        df['volume_ratio_20'] = df['volume'] / df['volume_ma_20']
        
        # RSI (Relative Strength Index)
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # Support and Resistance levels
        df['resistance'] = df['high'].rolling(window=20).max()
        df['support'] = df['low'].rolling(window=20).min()
        df['resistance_distance'] = (df['resistance'] - df['close']) / df['close']
        df['support_distance'] = (df['close'] - df['support']) / df['close']
        
        return df
    
    def create_target_variable(self, df: pd.DataFrame, prediction_days: int = 30) -> pd.DataFrame:
        """
        Create target variable for prediction (1 if price increases in next N days, 0 otherwise)
        
        Args:
            df: DataFrame with price data
            prediction_days: Number of days to look ahead
            
        Returns:
            DataFrame with target variable
        """
        df = df.copy()
        
        # Calculate future return
        df['future_price'] = df['close'].shift(-prediction_days)
        df['future_return'] = (df['future_price'] - df['close']) / df['close']
        
        # Binary classification: 1 if price increases by more than 5%, 0 otherwise
        df['target'] = (df['future_return'] > 0.05).astype(int)
        
        # Remove rows where we don't have future data
        df = df.dropna(subset=['target'])
        
        return df
    
    def prepare_features(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """
        Prepare features for ML model
        
        Args:
            df: DataFrame with technical features and target
            
        Returns:
            Tuple of (features, target)
        """
        # Select feature columns (exclude price columns and target)
        feature_cols = [col for col in df.columns if col not in [
            'time', 'open', 'high', 'low', 'close', 'volume', 
            'future_price', 'future_return', 'target'
        ] and not col.startswith('ma_')]  # Exclude raw moving averages, keep ratios
        
        # Remove columns with too many NaN values
        feature_cols = [col for col in feature_cols if df[col].notna().sum() > len(df) * 0.7]
        
        self.feature_names = feature_cols
        
        # Get features and target
        X = df[feature_cols].fillna(method='ffill').fillna(0)
        y = df['target']
        
        return X.values, y.values
    
    def train_model(self, X: np.ndarray, y: np.ndarray, model_type: str = 'random_forest') -> Dict:
        """
        Train the prediction model
        
        Args:
            X: Feature matrix
            y: Target vector
            model_type: Type of model to use
            
        Returns:
            Training results dictionary
        """
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Initialize model
        if model_type == 'random_forest':
            self.model = RandomForestClassifier(
                n_estimators=100, 
                max_depth=10, 
                random_state=42,
                class_weight='balanced'
            )
        elif model_type == 'gradient_boosting':
            self.model = GradientBoostingClassifier(
                n_estimators=100,
                max_depth=6,
                random_state=42
            )
        else:  # logistic_regression
            self.model = LogisticRegression(
                random_state=42,
                class_weight='balanced'
            )
        
        # Train model
        self.model.fit(X_train_scaled, y_train)
        
        # Make predictions
        y_pred = self.model.predict(X_test_scaled)
        y_pred_proba = self.model.predict_proba(X_test_scaled)[:, 1]
        
        # Calculate metrics
        accuracy = accuracy_score(y_test, y_pred)
        
        self.is_trained = True
        
        return {
            'accuracy': accuracy,
            'y_test': y_test,
            'y_pred': y_pred,
            'y_pred_proba': y_pred_proba,
            'feature_importance': self.get_feature_importance()
        }
    
    def get_feature_importance(self) -> pd.DataFrame:
        """
        Get feature importance from trained model
        
        Returns:
            DataFrame with feature importance
        """
        if not self.is_trained or self.model is None:
            return pd.DataFrame()
        
        if hasattr(self.model, 'feature_importances_'):
            importance = self.model.feature_importances_
        elif hasattr(self.model, 'coef_'):
            importance = np.abs(self.model.coef_[0])
        else:
            return pd.DataFrame()
        
        feature_importance = pd.DataFrame({
            'feature': self.feature_names,
            'importance': importance
        }).sort_values('importance', ascending=False)
        
        return feature_importance
    
    def predict_probability(self, X: np.ndarray) -> float:
        """
        Predict probability of price increase
        
        Args:
            X: Feature matrix for prediction
            
        Returns:
            Probability of price increase
        """
        if not self.is_trained or self.model is None:
            return 0.5
        
        X_scaled = self.scaler.transform(X)
        probability = self.model.predict_proba(X_scaled)[:, 1]
        
        return probability[0] if len(probability) > 0 else 0.5

def fetch_stock_data_for_prediction(symbol: str, days: int = 365) -> pd.DataFrame:
    """
    Fetch stock data for prediction model
    
    Args:
        symbol: Stock symbol
        days: Number of days of historical data
        
    Returns:
        DataFrame with stock data
    """
    try:
        stock = Vnstock().stock(symbol=symbol, source='TCBS')
        end_date = datetime.datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.datetime.now() - datetime.timedelta(days=days)).strftime('%Y-%m-%d')
        
        df = stock.quote.history(symbol=symbol, start=start_date, end=end_date)
        
        if df.empty:
            st.warning(f"No data available for {symbol}")
            return pd.DataFrame()
        
        return df
        
    except Exception as e:
        st.error(f"Error fetching data for {symbol}: {str(e)}")
        return pd.DataFrame()

def create_prediction_dashboard(symbol: str) -> None:
    """
    Create prediction dashboard for a specific stock
    
    Args:
        symbol: Stock symbol to analyze
    """
    st.subheader(f"🔮 Price Prediction for {symbol}")
    
    # Fetch data
    with st.spinner("Fetching historical data..."):
        df = fetch_stock_data_for_prediction(symbol, days=730)  # 2 years of data
    
    if df.empty:
        st.error("No data available for prediction")
        return
    
    # Initialize model
    model = StockPredictionModel()
    
    # Create features
    with st.spinner("Creating technical features..."):
        df_features = model.create_technical_features(df)
        df_with_target = model.create_target_variable(df_features, prediction_days=30)
    
    if len(df_with_target) < 100:
        st.warning("Insufficient data for reliable prediction (need at least 100 data points)")
        return
    
    # Prepare data
    X, y = model.prepare_features(df_with_target)
    
    if X.shape[0] == 0:
        st.error("No valid features could be created")
        return
    
    # Model selection
    col1, col2 = st.columns(2)
    
    with col1:
        model_type = st.selectbox(
            "Select Model Type",
            ['random_forest', 'gradient_boosting', 'logistic_regression'],
            help="Choose the machine learning algorithm for prediction"
        )
    
    with col2:
        if st.button("🚀 Train Model", type="primary"):
            with st.spinner("Training model..."):
                results = model.train_model(X, y, model_type)
                st.session_state[f'model_results_{symbol}'] = results
                st.session_state[f'trained_model_{symbol}'] = model
    
    # Display results if model is trained
    if f'model_results_{symbol}' in st.session_state:
        results = st.session_state[f'model_results_{symbol}']
        trained_model = st.session_state[f'trained_model_{symbol}']
        
        # Model performance
        st.subheader("📊 Model Performance")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Accuracy", f"{results['accuracy']:.2%}")
        
        with col2:
            # Calculate precision for positive class
            from sklearn.metrics import precision_score
            precision = precision_score(results['y_test'], results['y_pred'])
            st.metric("Precision", f"{precision:.2%}")
        
        with col3:
            # Calculate recall for positive class
            from sklearn.metrics import recall_score
            recall = recall_score(results['y_test'], results['y_pred'])
            st.metric("Recall", f"{recall:.2%}")
        
        # Feature importance
        st.subheader("🎯 Feature Importance")
        feature_importance = results['feature_importance']
        
        if not feature_importance.empty:
            fig_importance = px.bar(
                feature_importance.head(10),
                x='importance',
                y='feature',
                orientation='h',
                title="Top 10 Most Important Features"
            )
            fig_importance.update_layout(height=400)
            st.plotly_chart(fig_importance, use_container_width=True)
        
        # Current prediction
        st.subheader("🔮 Current Prediction")
        
        # Get latest features for prediction
        latest_features = df_features.iloc[-1:][trained_model.feature_names].fillna(method='ffill').fillna(0)
        
        if not latest_features.empty:
            probability = trained_model.predict_probability(latest_features.values)
            
            # Create gauge chart for probability
            fig_gauge = go.Figure(go.Indicator(
                mode = "gauge+number+delta",
                value = probability * 100,
                domain = {'x': [0, 1], 'y': [0, 1]},
                title = {'text': "Probability of 5%+ Increase in Next 30 Days"},
                delta = {'reference': 50},
                gauge = {
                    'axis': {'range': [0, 100]},
                    'bar': {'color': "darkblue"},
                    'steps': [
                        {'range': [0, 30], 'color': "lightgray"},
                        {'range': [30, 70], 'color': "gray"},
                        {'range': [70, 100], 'color': "lightgreen"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 50
                    }
                }
            ))
            
            fig_gauge.update_layout(height=400)
            st.plotly_chart(fig_gauge, use_container_width=True)
            
            # Interpretation
            if probability > 0.7:
                st.success(f"🚀 High probability ({probability:.1%}) of significant price increase!")
            elif probability > 0.5:
                st.info(f"📈 Moderate probability ({probability:.1%}) of price increase.")
            else:
                st.warning(f"📉 Low probability ({probability:.1%}) of significant price increase.")
        
        # Model explanation
        st.subheader("📝 Model Explanation")
        st.write("""
        **How the prediction works:**
        
        1. **Technical Features**: The model uses 20+ technical indicators including moving averages, RSI, MACD, Bollinger Bands, and volume patterns.
        
        2. **Target Definition**: The model predicts whether the stock price will increase by more than 5% in the next 30 days.
        
        3. **Training Data**: Uses historical data to learn patterns between technical indicators and future price movements.
        
        4. **Probability Score**: The output is a probability between 0% and 100%, where higher values indicate stronger bullish signals.
        
        **Important Notes:**
        - This is for educational purposes only and should not be used as sole investment advice
        - Past performance does not guarantee future results
        - Consider multiple factors including fundamental analysis and market conditions
        """)
    
    else:
        st.info("👆 Click 'Train Model' to generate predictions for this stock.")
