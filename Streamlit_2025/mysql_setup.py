#!/usr/bin/env python3
"""
MySQL Database Setup and Configuration
======================================

This script helps set up MySQL database for the Streamlit admin system.
"""

import os
import mysql.connector
from mysql.connector import Error
import getpass
import sys

def test_mysql_connection(config, with_database=True):
    """Test MySQL connection with given config"""
    try:
        # Create a copy of config for testing
        test_config = config.copy()

        # If testing without database, remove database from config
        if not with_database and 'database' in test_config:
            del test_config['database']

        connection = mysql.connector.connect(**test_config)
        if connection.is_connected():
            if with_database:
                print("✅ MySQL connection with database successful!")
            else:
                print("✅ MySQL server connection successful!")
            connection.close()
            return True
    except Error as e:
        if with_database:
            print(f"❌ MySQL connection with database failed: {e}")
        else:
            print(f"❌ MySQL server connection failed: {e}")
        return False

def create_mysql_config():
    """Interactive MySQL configuration setup"""
    print("🔧 MySQL Database Configuration Setup")
    print("=" * 50)
    
    # Get MySQL connection details
    print("\n📋 Enter your MySQL connection details:")
    
    host = input("MySQL Host (default: localhost): ").strip() or "localhost"
    port = input("MySQL Port (default: 3306): ").strip() or "3306"
    
    try:
        port = int(port)
    except ValueError:
        print("❌ Invalid port number, using default 3306")
        port = 3306
    
    user = input("MySQL Username (default: root): ").strip() or "root"
    password = getpass.getpass("MySQL Password: ")
    
    database = input("Database Name (default: streamlit_analytics): ").strip() or "streamlit_analytics"
    
    # Test connection without database first
    test_config = {
        'host': host,
        'port': port,
        'user': user,
        'password': password
    }
    
    print(f"\n🔍 Testing connection to MySQL server {host}:{port}...")

    # First test connection to server without database
    if not test_mysql_connection(test_config, with_database=False):
        print("❌ Cannot connect to MySQL server. Please check your credentials.")
        return None

    # Try to create database if it doesn't exist
    try:
        connection = mysql.connector.connect(**test_config)
        cursor = connection.cursor()

        # Check if database exists
        cursor.execute(f"SHOW DATABASES LIKE '{database}'")
        db_exists = cursor.fetchone()

        if db_exists:
            print(f"✅ Database '{database}' already exists!")
        else:
            cursor.execute(f"CREATE DATABASE {database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"✅ Database '{database}' created successfully!")

        cursor.close()
        connection.close()

    except Error as e:
        print(f"⚠️ Warning: Could not create database: {e}")
        print("You may need to create the database manually.")

        # Ask if user wants to continue without database creation
        continue_anyway = input("Continue anyway? (y/n): ").strip().lower()
        if continue_anyway != 'y':
            return None

    # Final config with database
    final_config = {
        'host': host,
        'port': port,
        'user': user,
        'password': password,
        'database': database,
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }

    # Test final connection with database
    print(f"\n🔍 Testing connection to database '{database}'...")
    if test_mysql_connection(final_config, with_database=True):
        return final_config
    else:
        print("❌ Cannot connect to the database.")
        print("💡 The server connection works, but database access failed.")
        print("💡 This might be a permissions issue.")
        return None

def create_env_file(config):
    """Create .env file with MySQL configuration"""
    env_content = f"""# MySQL Database Configuration for Streamlit Admin System
MYSQL_HOST={config['host']}
MYSQL_PORT={config['port']}
MYSQL_DATABASE={config['database']}
MYSQL_USER={config['user']}
MYSQL_PASSWORD={config['password']}
"""
    
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        print("✅ .env file created successfully!")
        print("📁 Location: .env")
        return True
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        return False

def create_config_template():
    """Create a configuration template file"""
    template_content = """# MySQL Configuration Template
# Copy this to .env and fill in your actual values

MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=streamlit_2025_analytics
MYSQL_USER=root
MYSQL_PASSWORD=HnAm2002#@!
"""
    
    try:
        with open('mysql_config_template.txt', 'w') as f:
            f.write(template_content)
        print("✅ Configuration template created: mysql_config_template.txt")
        return True
    except Exception as e:
        print(f"❌ Error creating template: {e}")
        return False

def initialize_database(config):
    """Initialize the database with tables"""
    print("\n🗄️ Initializing database tables...")
    
    try:
        from mysql_database import mysql_db
        
        # Update the global config
        mysql_db.config = config
        
        # Initialize database
        if mysql_db.initialize_database():
            print("✅ Database tables initialized successfully!")
            return True
        else:
            print("❌ Database initialization failed!")
            return False
            
    except ImportError:
        print("❌ Cannot import mysql_database module. Make sure it exists.")
        return False
    except Exception as e:
        print(f"❌ Database initialization error: {e}")
        return False

def verify_installation():
    """Verify the complete installation"""
    print("\n🔍 Verifying installation...")
    
    # Check if .env file exists
    if os.path.exists('.env'):
        print("✅ .env file found")
    else:
        print("❌ .env file not found")
        return False
    
    # Check if mysql_database module can be imported
    try:
        from mysql_database import mysql_db, mysql_user_tracker, mysql_support_system
        print("✅ MySQL database modules imported successfully")
    except ImportError as e:
        print(f"❌ Cannot import MySQL modules: {e}")
        return False
    
    # Test database connection
    try:
        from mysql_database import mysql_db
        
        with mysql_db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
            if result:
                print("✅ Database connection test passed")
            else:
                print("❌ Database connection test failed")
                return False
                
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return False
    
    print("✅ Installation verification completed successfully!")
    return True

def main():
    """Main setup function"""
    print("🚀 MySQL Database Setup for Streamlit Admin System")
    print("This script will help you configure MySQL for user analytics and support.")
    print()
    
    # Check if MySQL connector is installed
    try:
        import mysql.connector
        print("✅ mysql-connector-python is installed")
    except ImportError:
        print("❌ mysql-connector-python is not installed!")
        print("Please install it with: pip install mysql-connector-python")
        return
    
    # Menu
    while True:
        print("\n" + "=" * 50)
        print("Choose an option:")
        print("1. 🔧 Configure MySQL connection")
        print("2. 📄 Create configuration template")
        print("3. 🗄️ Initialize database tables")
        print("4. 🔍 Verify installation")
        print("5. 📋 Show current configuration")
        print("6. 🚪 Exit")
        
        choice = input("\nEnter your choice (1-6): ").strip()
        
        if choice == "1":
            config = create_mysql_config()
            if config:
                if create_env_file(config):
                    print("\n🎉 MySQL configuration completed!")
                    print("You can now use the admin system with persistent chat channels.")
                    
                    # Ask if user wants to initialize database
                    init_db = input("\nInitialize database tables now? (y/n): ").strip().lower()
                    if init_db == 'y':
                        initialize_database(config)
        
        elif choice == "2":
            create_config_template()
        
        elif choice == "3":
            # Load config from .env
            if os.path.exists('.env'):
                # Load environment variables
                with open('.env', 'r') as f:
                    for line in f:
                        if '=' in line and not line.startswith('#'):
                            key, value = line.strip().split('=', 1)
                            os.environ[key] = value
                
                config = {
                    'host': os.getenv('MYSQL_HOST', 'localhost'),
                    'port': int(os.getenv('MYSQL_PORT', 3306)),
                    'database': os.getenv('MYSQL_DATABASE', 'streamlit_analytics'),
                    'user': os.getenv('MYSQL_USER', 'root'),
                    'password': os.getenv('MYSQL_PASSWORD', ''),
                    'charset': 'utf8mb4',
                    'collation': 'utf8mb4_unicode_ci'
                }
                
                initialize_database(config)
            else:
                print("❌ .env file not found. Please configure MySQL first (option 1).")
        
        elif choice == "4":
            verify_installation()
        
        elif choice == "5":
            if os.path.exists('.env'):
                print("\n📋 Current configuration (.env):")
                with open('.env', 'r') as f:
                    for line in f:
                        if not line.startswith('#') and '=' in line:
                            key, value = line.strip().split('=', 1)
                            if 'PASSWORD' in key:
                                print(f"{key}=***hidden***")
                            else:
                                print(f"{key}={value}")
            else:
                print("❌ No configuration found. Please run option 1 first.")
        
        elif choice == "6":
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice. Please enter 1-6.")

if __name__ == "__main__":
    main()
