# Job Search Feature Dependencies
# Install with: pip install -r job_requirements.txt

# Core job crawling
crawl4ai>=0.2.0
beautifulsoup4>=4.12.0
requests>=2.31.0

# Google AI for LLM
google-generativeai>=0.3.0

# Document processing for CV upload
PyPDF2>=3.0.0
python-docx>=0.8.11

# RAG and embeddings
sentence-transformers>=2.2.0
scikit-learn>=1.3.0
numpy>=1.24.0

# Data processing
pandas>=2.0.0

# Visualization
plotly>=5.15.0

# Optional: For better text processing
nltk>=3.8
spacy>=3.6.0
