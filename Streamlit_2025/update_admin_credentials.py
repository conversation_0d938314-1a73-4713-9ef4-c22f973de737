#!/usr/bin/env python3
"""
Script to update admin credentials in config.yaml
"""

import yaml
import bcrypt
import getpass
import os
from datetime import datetime

def hash_password(password: str) -> str:
    """Hash a password using bcrypt"""
    # Generate salt and hash password
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    """Verify a password against its hash"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def load_config():
    """Load the current config.yaml"""
    try:
        with open('config.yaml', 'r') as file:
            return yaml.safe_load(file)
    except FileNotFoundError:
        print("❌ config.yaml not found!")
        return None
    except Exception as e:
        print(f"❌ Error loading config.yaml: {str(e)}")
        return None

def save_config(config):
    """Save the updated config.yaml"""
    try:
        # Backup original config
        backup_name = f"config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
        with open(backup_name, 'w') as backup_file:
            yaml.dump(config, backup_file, default_flow_style=False)
        print(f"✅ Backup created: {backup_name}")
        
        # Save updated config
        with open('config.yaml', 'w') as file:
            yaml.dump(config, file, default_flow_style=False)
        print("✅ config.yaml updated successfully!")
        return True
    except Exception as e:
        print(f"❌ Error saving config.yaml: {str(e)}")
        return False

def update_admin_credentials():
    """Update admin credentials interactively"""
    print("🔧 Admin Credentials Update Tool")
    print("=" * 40)
    
    # Load current config
    config = load_config()
    if not config:
        return False
    
    # Display current admin users
    print("\n📋 Current Admin Users:")
    usernames = config.get('credentials', {}).get('usernames', {})
    admin_users = []
    
    for username, user_data in usernames.items():
        roles = user_data.get('roles', [])
        if roles and 'admin' in roles:
            admin_users.append(username)
            print(f"  - {username} ({user_data.get('first_name', '')} {user_data.get('last_name', '')})")
    
    if not admin_users:
        print("  No admin users found!")
        return False
    
    print("\n" + "=" * 40)
    
    # Choose what to do
    print("\nChoose an option:")
    print("1. Update existing admin password")
    print("2. Create new admin user")
    print("3. Change admin username")
    print("4. Update admin details (name, email)")
    print("5. Exit")
    
    choice = input("\nEnter your choice (1-5): ").strip()
    
    if choice == "1":
        return update_existing_admin_password(config)
    elif choice == "2":
        return create_new_admin_user(config)
    elif choice == "3":
        return change_admin_username(config)
    elif choice == "4":
        return update_admin_details(config)
    elif choice == "5":
        print("👋 Goodbye!")
        return True
    else:
        print("❌ Invalid choice!")
        return False

def update_existing_admin_password(config):
    """Update password for existing admin"""
    usernames = config.get('credentials', {}).get('usernames', {})
    admin_users = [u for u, d in usernames.items() if d.get('roles') and 'admin' in d.get('roles')]
    
    if len(admin_users) == 1:
        username = admin_users[0]
        print(f"\n🔑 Updating password for admin user: {username}")
    else:
        print(f"\n🔑 Select admin user to update:")
        for i, user in enumerate(admin_users, 1):
            print(f"  {i}. {user}")
        
        try:
            choice = int(input("Enter number: ")) - 1
            username = admin_users[choice]
        except (ValueError, IndexError):
            print("❌ Invalid selection!")
            return False
    
    # Get new password
    print(f"\n🔐 Enter new password for '{username}':")
    new_password = getpass.getpass("New password: ")
    confirm_password = getpass.getpass("Confirm password: ")
    
    if new_password != confirm_password:
        print("❌ Passwords don't match!")
        return False
    
    if len(new_password) < 6:
        print("❌ Password must be at least 6 characters!")
        return False
    
    # Hash new password
    hashed_password = hash_password(new_password)
    
    # Update config
    config['credentials']['usernames'][username]['password'] = hashed_password
    config['credentials']['usernames'][username]['failed_login_attempts'] = 0
    
    # Save config
    if save_config(config):
        print(f"✅ Password updated successfully for '{username}'!")
        print(f"🔑 New login credentials:")
        print(f"   Username: {username}")
        print(f"   Password: {new_password}")
        return True
    
    return False

def create_new_admin_user(config):
    """Create a new admin user"""
    print("\n👤 Create New Admin User")
    
    # Get user details
    username = input("Username: ").strip()
    if not username:
        print("❌ Username cannot be empty!")
        return False
    
    # Check if username exists
    usernames = config.get('credentials', {}).get('usernames', {})
    if username in usernames:
        print(f"❌ Username '{username}' already exists!")
        return False
    
    first_name = input("First Name: ").strip()
    last_name = input("Last Name: ").strip()
    email = input("Email: ").strip()
    
    # Get password
    password = getpass.getpass("Password: ")
    confirm_password = getpass.getpass("Confirm password: ")
    
    if password != confirm_password:
        print("❌ Passwords don't match!")
        return False
    
    if len(password) < 6:
        print("❌ Password must be at least 6 characters!")
        return False
    
    # Hash password
    hashed_password = hash_password(password)
    
    # Create user data
    user_data = {
        'email': email,
        'failed_login_attempts': 0,
        'first_name': first_name,
        'last_name': last_name,
        'logged_in': False,
        'password': hashed_password,
        'roles': ['admin']
    }
    
    # Add to config
    if 'credentials' not in config:
        config['credentials'] = {}
    if 'usernames' not in config['credentials']:
        config['credentials']['usernames'] = {}
    
    config['credentials']['usernames'][username] = user_data
    
    # Save config
    if save_config(config):
        print(f"✅ Admin user '{username}' created successfully!")
        print(f"🔑 Login credentials:")
        print(f"   Username: {username}")
        print(f"   Password: {password}")
        return True
    
    return False

def change_admin_username(config):
    """Change admin username"""
    usernames = config.get('credentials', {}).get('usernames', {})
    admin_users = [u for u, d in usernames.items() if d.get('roles') and 'admin' in d.get('roles')]
    
    if not admin_users:
        print("❌ No admin users found!")
        return False
    
    print(f"\n📝 Select admin user to rename:")
    for i, user in enumerate(admin_users, 1):
        print(f"  {i}. {user}")
    
    try:
        choice = int(input("Enter number: ")) - 1
        old_username = admin_users[choice]
    except (ValueError, IndexError):
        print("❌ Invalid selection!")
        return False
    
    new_username = input(f"New username for '{old_username}': ").strip()
    if not new_username:
        print("❌ Username cannot be empty!")
        return False
    
    if new_username in usernames:
        print(f"❌ Username '{new_username}' already exists!")
        return False
    
    # Move user data to new username
    user_data = usernames[old_username].copy()
    config['credentials']['usernames'][new_username] = user_data
    del config['credentials']['usernames'][old_username]
    
    # Save config
    if save_config(config):
        print(f"✅ Username changed from '{old_username}' to '{new_username}'!")
        return True
    
    return False

def update_admin_details(config):
    """Update admin user details"""
    usernames = config.get('credentials', {}).get('usernames', {})
    admin_users = [u for u, d in usernames.items() if d.get('roles') and 'admin' in d.get('roles')]
    
    if not admin_users:
        print("❌ No admin users found!")
        return False
    
    print(f"\n📝 Select admin user to update:")
    for i, user in enumerate(admin_users, 1):
        user_data = usernames[user]
        print(f"  {i}. {user} ({user_data.get('first_name', '')} {user_data.get('last_name', '')})")
    
    try:
        choice = int(input("Enter number: ")) - 1
        username = admin_users[choice]
    except (ValueError, IndexError):
        print("❌ Invalid selection!")
        return False
    
    user_data = usernames[username]
    
    print(f"\n📝 Update details for '{username}':")
    print(f"Current first name: {user_data.get('first_name', '')}")
    new_first_name = input("New first name (press Enter to keep current): ").strip()
    if new_first_name:
        user_data['first_name'] = new_first_name
    
    print(f"Current last name: {user_data.get('last_name', '')}")
    new_last_name = input("New last name (press Enter to keep current): ").strip()
    if new_last_name:
        user_data['last_name'] = new_last_name
    
    print(f"Current email: {user_data.get('email', '')}")
    new_email = input("New email (press Enter to keep current): ").strip()
    if new_email:
        user_data['email'] = new_email
    
    # Save config
    if save_config(config):
        print(f"✅ Details updated for '{username}'!")
        return True
    
    return False

def main():
    """Main function"""
    print("🚀 Admin Credentials Management Tool")
    print("This tool helps you manage admin user credentials for your Streamlit app.\n")
    
    # Check if config.yaml exists
    if not os.path.exists('config.yaml'):
        print("❌ config.yaml not found in current directory!")
        print("Please run this script from the same directory as your config.yaml file.")
        return
    
    try:
        while True:
            success = update_admin_credentials()
            if success:
                break
            
            retry = input("\n🔄 Try again? (y/n): ").strip().lower()
            if retry != 'y':
                break
    
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")

if __name__ == "__main__":
    main()
