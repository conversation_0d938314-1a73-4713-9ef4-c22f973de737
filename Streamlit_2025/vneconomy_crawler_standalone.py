"""
VnEconomy News Crawler - Standalone Version for Web Apps
========================================================

Clean, standalone version of the VnEconomy crawler optimized for web application use.
This file can be copied directly to your web app project.
"""

import asyncio
import json
import csv
import os
from datetime import datetime
from typing import List, Dict, Optional
from urllib.parse import urljoin

from crawl4ai import AsyncWebCrawler, BrowserConfig, CrawlerRunConfig
from bs4 import BeautifulSoup


class CrawlerConfig:
    """Configuration settings for the VnEconomy crawler"""
    BASE_URL = "https://vneconomy.vn/chung-khoan.htm"
    DOMAIN = "https://vneconomy.vn"
    DEFAULT_MAX_PAGES = 5
    DEFAULT_TIMEOUT = 30000
    OUTPUT_DIR = "data"
    
    # CSS Selectors
    MAIN_CONTAINER = "div.col-12.col-lg-9.column-border"
    ARTICLE_SELECTOR = "article.story.story--featured.story--timeline"
    TIME_SELECTOR = "time"
    TITLE_SELECTOR = "h3.story__title"
    PAGINATION_SELECTOR = "ul.pagination"
    PAGE_ITEM_SELECTOR = "li.page-item"
    
    @classmethod
    def get_output_path(cls, filename: str) -> str:
        """Get full path for output file"""
        os.makedirs(cls.OUTPUT_DIR, exist_ok=True)
        return os.path.join(cls.OUTPUT_DIR, filename)


class VnEconomyNewsCrawler:
    """
    Standalone VnEconomy news crawler for web applications
    """
    
    def __init__(self, headless: bool = True, use_managed_browser: bool = False):
        """
        Initialize the crawler
        
        Args:
            headless: Whether to run browser in headless mode
            use_managed_browser: Whether to use managed browser for identity preservation
        """
        self.config = CrawlerConfig()
        self.articles = []
        
        # Browser configuration
        if use_managed_browser:
            self.browser_config = BrowserConfig(
                headless=headless,
                use_managed_browser=True,
                user_data_dir="~/vneconomy_profile",
                browser_type="chromium",
                verbose=False
            )
        else:
            self.browser_config = BrowserConfig(
                headless=headless,
                browser_type="chromium",
                verbose=False
            )
        
        # Crawl configuration
        self.crawl_config = CrawlerRunConfig(
            wait_for=f"css:{self.config.MAIN_CONTAINER}",
            page_timeout=self.config.DEFAULT_TIMEOUT,
            remove_overlay_elements=True,
            magic=True
        )
    
    def extract_articles_from_html(self, html_content: str, page_url: str) -> List[Dict]:
        """
        Extract articles from HTML content
        
        Args:
            html_content: Raw HTML content
            page_url: URL of the page being processed
            
        Returns:
            List of article dictionaries
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        articles = []
        
        # Find the main content container
        main_container = soup.find('div', class_='col-12 col-lg-9 column-border')
        if not main_container:
            return articles
        
        # Find all article elements
        article_elements = main_container.find_all('article', class_='story story--featured story--timeline')
        
        for article in article_elements:
            try:
                # Extract time
                time_element = article.find('time')
                time_value = time_element.get_text(strip=True) if time_element else "No time"
                
                # Extract title
                title_element = article.find('h3', class_='story__title')
                title = title_element.get_text(strip=True) if title_element else "No title"
                
                # Extract article link if available
                link_element = title_element.find('a') if title_element else None
                article_url = None
                if link_element and link_element.get('href'):
                    article_url = urljoin(self.config.DOMAIN, link_element.get('href'))
                
                article_data = {
                    'title': title,
                    'time': time_value,
                    'url': article_url,
                    'page_url': page_url,
                    'extracted_at': datetime.now().isoformat()
                }
                
                articles.append(article_data)
                
            except Exception as e:
                # Log error but continue processing
                print(f"Error extracting article: {e}")
                continue
        
        return articles
    
    def extract_pagination_info(self, html_content: str) -> Dict:
        """
        Extract pagination information from HTML
        
        Args:
            html_content: Raw HTML content
            
        Returns:
            Dictionary with pagination info
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        pagination_info = {
            'current_page': 1,
            'total_pages': 1,
            'next_page_url': None,
            'page_urls': []
        }
        
        # Find pagination container
        pagination_container = soup.find('ul', class_='pagination')
        if not pagination_container:
            return pagination_info
        
        # Find all page items
        page_items = pagination_container.find_all('li', class_='page-item')
        
        for item in page_items:
            # Check if this is the active page
            if 'active' in item.get('class', []):
                page_link = item.find('a')
                if page_link:
                    try:
                        pagination_info['current_page'] = int(page_link.get_text(strip=True))
                    except ValueError:
                        pass
            
            # Extract all page URLs
            page_link = item.find('a')
            if page_link and page_link.get('href'):
                page_url = urljoin(self.config.DOMAIN, page_link.get('href'))
                pagination_info['page_urls'].append({
                    'page_number': page_link.get_text(strip=True),
                    'url': page_url,
                    'title': page_link.get('title', ''),
                    'is_active': 'active' in item.get('class', [])
                })
        
        # Determine total pages and next page
        if pagination_info['page_urls']:
            numeric_pages = []
            for page_info in pagination_info['page_urls']:
                try:
                    page_num = int(page_info['page_number'])
                    numeric_pages.append(page_num)
                except ValueError:
                    continue
            
            if numeric_pages:
                pagination_info['total_pages'] = max(numeric_pages)
                
                # Find next page URL
                current_page = pagination_info['current_page']
                for page_info in pagination_info['page_urls']:
                    try:
                        if int(page_info['page_number']) == current_page + 1:
                            pagination_info['next_page_url'] = page_info['url']
                            break
                    except ValueError:
                        continue
        
        return pagination_info
    
    async def crawl_page(self, url: str, crawler: AsyncWebCrawler) -> Dict:
        """
        Crawl a single page and extract articles
        
        Args:
            url: URL to crawl
            crawler: AsyncWebCrawler instance
            
        Returns:
            Dictionary with articles and pagination info
        """
        result = await crawler.arun(url=url, config=self.crawl_config)
        
        if not result.success:
            return {'articles': [], 'pagination': None, 'error': result.error_message}
        
        # Extract articles and pagination
        articles = self.extract_articles_from_html(result.html, url)
        pagination = self.extract_pagination_info(result.html)
        
        return {
            'articles': articles,
            'pagination': pagination,
            'error': None
        }
    
    async def crawl_all_pages(self, max_pages: Optional[int] = None) -> List[Dict]:
        """
        Crawl all pages with pagination
        
        Args:
            max_pages: Maximum number of pages to crawl (None for all)
            
        Returns:
            List of all articles
        """
        all_articles = []
        current_url = self.config.BASE_URL
        pages_crawled = 0
        
        if max_pages is None:
            max_pages = self.config.DEFAULT_MAX_PAGES
        
        async with AsyncWebCrawler(config=self.browser_config) as crawler:
            while current_url and pages_crawled < max_pages:
                pages_crawled += 1
                
                # Crawl current page
                page_data = await self.crawl_page(current_url, crawler)
                
                if page_data['error']:
                    print(f"Error crawling page {pages_crawled}: {page_data['error']}")
                    break
                
                # Add articles to collection
                articles = page_data['articles']
                all_articles.extend(articles)
                self.articles.extend(articles)
                
                # Get next page URL
                pagination = page_data['pagination']
                if pagination and pagination['next_page_url']:
                    current_url = pagination['next_page_url']
                    # Add delay between requests to be respectful
                    await asyncio.sleep(2)
                else:
                    break
        
        return all_articles
    
    async def crawl_single_page(self) -> List[Dict]:
        """
        Crawl just the first page
        
        Returns:
            List of articles from first page
        """
        async with AsyncWebCrawler(config=self.browser_config) as crawler:
            page_data = await self.crawl_page(self.config.BASE_URL, crawler)
            
            if page_data['error']:
                raise Exception(f"Failed to crawl page: {page_data['error']}")
            
            articles = page_data['articles']
            self.articles = articles
            return articles
    
    def save_to_json(self, filename: str = None) -> str:
        """
        Save articles to JSON file
        
        Args:
            filename: Output filename (auto-generated if None)
            
        Returns:
            Path to saved file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"vneconomy_articles_{timestamp}.json"
        
        filepath = self.config.get_output_path(filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.articles, f, ensure_ascii=False, indent=2)
        
        return filepath
    
    def save_to_csv(self, filename: str = None) -> str:
        """
        Save articles to CSV file
        
        Args:
            filename: Output filename (auto-generated if None)
            
        Returns:
            Path to saved file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"vneconomy_articles_{timestamp}.csv"
        
        if not self.articles:
            raise ValueError("No articles to save")
        
        filepath = self.config.get_output_path(filename)
        
        with open(filepath, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=self.articles[0].keys())
            writer.writeheader()
            writer.writerows(self.articles)
        
        return filepath
    
    def get_articles_summary(self) -> Dict:
        """
        Get summary statistics of crawled articles
        
        Returns:
            Dictionary with summary statistics
        """
        if not self.articles:
            return {'total': 0, 'latest_time': None, 'oldest_time': None}
        
        times = [article['time'] for article in self.articles if article['time'] != 'No time']
        
        return {
            'total': len(self.articles),
            'latest_time': times[0] if times else None,
            'oldest_time': times[-1] if times else None,
            'unique_dates': len(set(times)) if times else 0
        }


# Example usage for web apps
async def main():
    """
    Example usage of the crawler
    """
    crawler = VnEconomyNewsCrawler(headless=True)
    
    # Crawl 2 pages
    articles = await crawler.crawl_all_pages(max_pages=2)
    
    print(f"Crawled {len(articles)} articles")
    
    # Save results
    json_file = crawler.save_to_json()
    csv_file = crawler.save_to_csv()
    
    print(f"Saved to: {json_file} and {csv_file}")
    
    # Get summary
    summary = crawler.get_articles_summary()
    print(f"Summary: {summary}")


if __name__ == "__main__":
    asyncio.run(main())
