# 🚀 AI-Powered Job Search Setup Guide

## 📋 Overview

The Job Search feature provides:
- **Job Crawling** from Vietnamese job sites (VietnamWorks, TopCV, CareerBuilder, JobsGo)
- **AI-Powered Matching** using Google's free LLM API
- **CV Analysis** with RAG (Retrieval-Augmented Generation)
- **Smart Job Recommendations** based on CV content

## 🛠️ Installation

### Step 1: Install Dependencies

```bash
# Install job search dependencies
pip install -r job_requirements.txt

# Or install individually:
pip install crawl4ai beautifulsoup4 requests
pip install google-generativeai
pip install PyPDF2 python-docx
pip install sentence-transformers scikit-learn
pip install plotly pandas numpy
```

### Step 2: Get Google AI API Key (Free)

1. **Go to Google AI Studio:**
   - Visit: https://makersuite.google.com/app/apikey
   - Sign in with your Google account

2. **Create API Key:**
   - Click "Create API Key"
   - Copy the generated key

3. **Configure API Key:**
   - Option 1: Set environment variable
     ```bash
     export GOOGLE_AI_API_KEY="your_api_key_here"
     ```
   - Option 2: Enter in app settings tab
   - Option 3: Add to Streamlit secrets (for deployment)

### Step 3: Test Installation

```bash
# Test the job crawler
python -c "
from job_crawler import search_jobs
import asyncio
jobs = asyncio.run(search_jobs('python developer', 'ho chi minh'))
print(f'Found {len(jobs)} jobs')
"

# Test AI matcher
python -c "
from job_ai_matcher import ai_matcher
print('AI matcher initialized:', ai_matcher.model is not None)
"
```

## 🎯 Features

### 🔍 **Job Crawling**
- **Multi-site Search:** Crawls 4+ Vietnamese job sites simultaneously
- **Smart Filtering:** By location, domain, keywords
- **Duplicate Removal:** Intelligent deduplication
- **Robust Error Handling:** Continues even if some sites fail

### 🤖 **AI Job Matching**
- **CV Analysis:** Extracts skills, experience, education from uploaded CVs
- **Semantic Matching:** Uses embeddings for skill similarity
- **LLM Scoring:** Google AI provides detailed match analysis
- **Personalized Advice:** Custom application tips for each job

### 📊 **Analytics Dashboard**
- **Market Insights:** Job distribution by location, company, source
- **Trend Analysis:** Hiring patterns and popular skills
- **Export Options:** CSV and JSON data export

### 📄 **CV Processing**
- **Multiple Formats:** PDF, DOCX, TXT support
- **Text Extraction:** Advanced document parsing
- **AI Analysis:** Automatic skill and experience extraction

## 🚀 Usage Guide

### Basic Job Search

1. **Navigate to Job Search tab**
2. **Enter search criteria:**
   - Job keyword (required)
   - Location (optional)
   - Industry domain (optional)
3. **Click "Search Jobs"**
4. **View results** with filtering and sorting options

### AI-Powered Matching

1. **Upload your CV** in the AI Matching tab
2. **Wait for AI analysis** of your skills and experience
3. **Search for jobs** in the Search tab
4. **Return to AI Matching** and click "Find Best Matches"
5. **View ranked results** with match scores and advice

### Analytics

1. **Search for jobs** first
2. **Go to Analytics tab**
3. **View market insights:**
   - Job distribution charts
   - Top hiring companies
   - Location trends
   - Source analysis

## 🔧 Configuration

### Supported Job Sites

Currently crawling:
- **VietnamWorks** (vietnamworks.com)
- **TopCV** (topcv.vn)
- **CareerBuilder** (careerbuilder.vn)
- **JobsGo** (jobsgo.vn)

### Search Parameters

- **Keyword:** Job title, skills, or company name
- **Location:** City or region (e.g., "Ho Chi Minh", "Hanoi")
- **Domain:** Industry category
- **Pages:** Number of pages to crawl per site (1-5)

### AI Configuration

- **Model:** Google Gemini Pro (free tier)
- **Embeddings:** SentenceTransformers (all-MiniLM-L6-v2)
- **Matching:** Hybrid AI + semantic similarity scoring

## 📈 Performance Tips

### For Better Results:
1. **Use specific keywords:** "Python Developer" vs "Developer"
2. **Include location:** Improves relevance
3. **Upload detailed CV:** More skills = better matching
4. **Try multiple searches:** Different keywords find different jobs

### For Faster Performance:
1. **Reduce pages per site:** Use 1-2 pages for quick results
2. **Use specific locations:** Reduces irrelevant results
3. **Cache results:** Results are stored in session

## 🛡️ Troubleshooting

### Common Issues:

#### "Job search feature not available"
- **Solution:** Install dependencies with `pip install -r job_requirements.txt`

#### "Google AI not available"
- **Solution:** Get free API key from Google AI Studio
- **Check:** API key is correctly configured

#### "No jobs found"
- **Try:** Different keywords or broader location
- **Check:** Internet connection and site accessibility

#### CV upload fails
- **Check:** File format (PDF, DOCX, TXT only)
- **Try:** Smaller file size or different format

#### Slow performance
- **Reduce:** Pages per site to 1-2
- **Check:** Internet connection speed
- **Wait:** Some job sites may be slow to respond

### Error Messages:

#### "Error crawling [site]"
- **Normal:** Some sites may be temporarily unavailable
- **Action:** Results from other sites will still show

#### "AI analysis failed"
- **Check:** Google AI API key is valid
- **Try:** Refresh page and try again

#### "Embedding model not available"
- **Install:** `pip install sentence-transformers`
- **Note:** AI matching will use basic scoring without embeddings

## 🔒 Privacy & Security

### Data Handling:
- **CV Content:** Processed locally, not stored permanently
- **Search Results:** Cached in browser session only
- **API Keys:** Stored securely, not logged

### Google AI Usage:
- **Free Tier:** 60 requests per minute
- **Data:** CV analysis requests sent to Google AI
- **Privacy:** Follow Google AI privacy policy

## 🚀 Deployment

### For Production:

1. **Set environment variables:**
   ```bash
   export GOOGLE_AI_API_KEY="your_key"
   ```

2. **Use Streamlit secrets:**
   ```toml
   # .streamlit/secrets.toml
   GOOGLE_AI_API_KEY = "your_key"
   ```

3. **Install dependencies:**
   ```bash
   pip install -r job_requirements.txt
   ```

4. **Test all features** before going live

## 📞 Support

### If you need help:
1. **Check this guide** first
2. **Verify dependencies** are installed
3. **Test API key** in settings tab
4. **Check error messages** for specific issues

### Feature Requests:
- Additional job sites
- More AI models
- Enhanced analytics
- Better CV parsing

## 🎉 Success!

Your AI-powered job search system is now ready! Users can:
- ✅ Search jobs across multiple Vietnamese sites
- ✅ Upload CVs for AI analysis
- ✅ Get personalized job recommendations
- ✅ View market analytics and trends
- ✅ Export data for further analysis

**The job search feature completes your Streamlit application with enterprise-grade job matching capabilities!** 🚀
