import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import asyncio
import json
import re
from datetime import datetime
from urllib.parse import urlparse
from textblob import TextBlob
from collections import Counter
from typing import List, Dict, Optional

class EnhancedSearchEngine:
    """Enhanced search engine with analytics and AI features"""
    
    def __init__(self):
        self.search_history = []
        self.results_cache = {}
        
    def _get_sentiment_label(self, polarity: float) -> str:
        """Convert sentiment polarity to label"""
        if polarity > 0.1:
            return "Positive"
        elif polarity < -0.1:
            return "Negative"
        else:
            return "Neutral"
    
    def analyze_search_results(self, results: List[Dict]) -> Dict:
        """
        Comprehensive analysis of search results
        
        Args:
            results: List of search results
            
        Returns:
            Analysis dictionary with insights
        """
        if not results:
            return {}
        
        df = pd.DataFrame(results)
        
        analysis = {
            "total_results": len(results),
            "unique_domains": df['domain'].nunique() if 'domain' in df.columns else 0,
            "top_domains": df['domain'].value_counts().head(10).to_dict() if 'domain' in df.columns else {},
            "sentiment_distribution": df['sentiment_label'].value_counts().to_dict() if 'sentiment_label' in df.columns else {},
            "avg_sentiment": df['sentiment_score'].mean() if 'sentiment_score' in df.columns else 0,
            "search_types": df['search_type'].value_counts().to_dict() if 'search_type' in df.columns else {},
            "date_range": {
                "earliest": df['timestamp'].min() if 'timestamp' in df.columns else None,
                "latest": df['timestamp'].max() if 'timestamp' in df.columns else None
            }
        }
        
        # Extract keywords from titles and descriptions
        if 'title' in df.columns and 'description' in df.columns:
            all_text = " ".join(df['title'].fillna("") + " " + df['description'].fillna(""))
            words = re.findall(r'\b[a-zA-Z]{3,}\b', all_text.lower())
            analysis["top_keywords"] = dict(Counter(words).most_common(20))
        else:
            analysis["top_keywords"] = {}
        
        return analysis
    
    def create_search_visualizations(self, results: List[Dict], analysis: Dict) -> None:
        """
        Create comprehensive visualizations for search results
        
        Args:
            results: Search results
            analysis: Analysis results
        """
        if not results:
            st.warning("No results to visualize")
            return
        
        df = pd.DataFrame(results)
        
        # 1. Domain Distribution
        st.subheader("🌐 Domain Analysis")
        col1, col2 = st.columns(2)
        
        with col1:
            if analysis.get("top_domains"):
                fig_domains = px.pie(
                    values=list(analysis["top_domains"].values()),
                    names=list(analysis["top_domains"].keys()),
                    title="Top Domains Distribution"
                )
                st.plotly_chart(fig_domains, use_container_width=True)
        
        with col2:
            # Sentiment distribution
            if analysis.get("sentiment_distribution"):
                fig_sentiment = px.bar(
                    x=list(analysis["sentiment_distribution"].keys()),
                    y=list(analysis["sentiment_distribution"].values()),
                    title="Sentiment Distribution",
                    color=list(analysis["sentiment_distribution"].keys()),
                    color_discrete_map={
                        "Positive": "#4ECDC4",
                        "Neutral": "#45B7D1", 
                        "Negative": "#FF6B6B"
                    }
                )
                st.plotly_chart(fig_sentiment, use_container_width=True)
        
        # 2. Keyword Cloud Visualization
        st.subheader("🔤 Top Keywords")
        if analysis.get("top_keywords"):
            keywords_df = pd.DataFrame(
                list(analysis["top_keywords"].items()), 
                columns=["Keyword", "Frequency"]
            ).head(15)
            
            fig_keywords = px.bar(
                keywords_df,
                x="Frequency",
                y="Keyword",
                orientation="h",
                title="Most Frequent Keywords",
                color="Frequency",
                color_continuous_scale="viridis"
            )
            fig_keywords.update_layout(height=500)
            st.plotly_chart(fig_keywords, use_container_width=True)

def create_advanced_search_interface():
    """Create the enhanced search interface"""
    st.title("🔍 AI-Powered Advanced Search Engine")
    
    # Initialize search engine
    if 'search_engine' not in st.session_state:
        st.session_state.search_engine = EnhancedSearchEngine()
    
    search_engine = st.session_state.search_engine
    
    # Search configuration
    st.subheader("🔍 Enhanced Search")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        search_query = st.text_input(
            "🔍 Enter search query:", 
            placeholder="e.g., artificial intelligence trends 2024", 
            key="enhanced_search_query"
        )
    
    with col2:
        search_type = st.selectbox(
            "Search Type",
            ["general", "news"],
            help="Choose the type of search to perform",
            key="enhanced_search_type"
        )
    
    with col3:
        pages_to_crawl = st.slider(
            "Pages to crawl", 
            1, 3, 1, 
            help="More pages = more results but slower", 
            key="enhanced_pages_crawl"
        )
    
    # Search button
    if st.button("🚀 Start Enhanced Search", type="primary", key="enhanced_search_btn"):
        if search_query:
            with st.spinner(f"Performing {search_type} search for '{search_query}'..."):
                try:
                    # Use the robust search function with fallback
                    from general_search import robust_search
                    results = asyncio.run(robust_search(search_query, pages=pages_to_crawl))
                    
                    if results:
                        # Convert to enhanced format with sentiment
                        enhanced_results = []
                        for result in results:
                            enhanced_result = {
                                "title": result.get("title", ""),
                                "url": result.get("url", ""),
                                "description": result.get("description", ""),
                                "domain": urlparse(result.get("url", "")).netloc if result.get("url") else "",
                                "search_type": search_type,
                                "timestamp": datetime.now().isoformat()
                            }
                            
                            # Add sentiment analysis
                            text_for_sentiment = f"{enhanced_result['title']} {enhanced_result['description']}"
                            if text_for_sentiment.strip():
                                blob = TextBlob(text_for_sentiment)
                                enhanced_result["sentiment_score"] = blob.sentiment.polarity
                                enhanced_result["sentiment_label"] = search_engine._get_sentiment_label(blob.sentiment.polarity)
                            else:
                                enhanced_result["sentiment_score"] = 0
                                enhanced_result["sentiment_label"] = "Neutral"
                            
                            enhanced_results.append(enhanced_result)
                        
                        # Store results
                        st.session_state.search_results = enhanced_results
                        st.session_state.search_query = search_query
                        
                        # Add to history
                        search_engine.search_history.append({
                            "query": search_query,
                            "type": search_type,
                            "timestamp": datetime.now(),
                            "results_count": len(enhanced_results)
                        })
                        
                        st.success(f"✅ Found {len(enhanced_results)} results!")
                    else:
                        st.warning("No results found. Try a different query.")
                        
                except Exception as e:
                    st.error(f"Search failed: {str(e)}")
                    st.info("This might be due to rate limiting or network issues. Please try again later.")
        else:
            st.warning("Please enter a search query.")
    
    # Display results if available
    if 'search_results' in st.session_state and st.session_state.search_results:
        results = st.session_state.search_results
        query = st.session_state.get('search_query', 'Unknown')
        
        # Analyze results
        analysis = search_engine.analyze_search_results(results)
        
        # Display analysis summary
        st.subheader(f"📊 Analysis Summary for '{query}'")
        
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Results", analysis.get("total_results", 0))
        with col2:
            st.metric("Unique Domains", analysis.get("unique_domains", 0))
        with col3:
            avg_sentiment = analysis.get("avg_sentiment", 0)
            sentiment_emoji = "😊" if avg_sentiment > 0.1 else "😐" if avg_sentiment > -0.1 else "😞"
            st.metric("Avg Sentiment", f"{avg_sentiment:.2f} {sentiment_emoji}")
        with col4:
            st.metric("Search Type", search_type.title())
        
        # Create visualizations
        search_engine.create_search_visualizations(results, analysis)
        
        # Display results table
        st.subheader("🔍 Search Results")
        
        # Filter options
        col1, col2 = st.columns(2)
        with col1:
            domain_filter = st.multiselect(
                "Filter by domain",
                options=list(analysis.get("top_domains", {}).keys()),
                help="Select domains to filter results",
                key="enhanced_domain_filter"
            )
        
        with col2:
            sentiment_filter = st.multiselect(
                "Filter by sentiment",
                options=["Positive", "Neutral", "Negative"],
                help="Filter results by sentiment",
                key="enhanced_sentiment_filter"
            )
        
        # Apply filters
        df = pd.DataFrame(results)
        if domain_filter:
            df = df[df['domain'].isin(domain_filter)]
        if sentiment_filter:
            df = df[df['sentiment_label'].isin(sentiment_filter)]
        
        # Display filtered results
        display_columns = ['title', 'domain', 'description', 'sentiment_label', 'url']
        available_columns = [col for col in display_columns if col in df.columns]
        
        st.dataframe(
            df[available_columns],
            use_container_width=True,
            hide_index=True,
            column_config={
                "url": st.column_config.LinkColumn("URL"),
                "sentiment_label": st.column_config.TextColumn("Sentiment"),
                "title": st.column_config.TextColumn("Title", width="large"),
                "description": st.column_config.TextColumn("Description", width="large")
            }
        )
        
        # Export options
        st.subheader("📥 Export Results")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            csv_data = df.to_csv(index=False)
            st.download_button(
                "📄 Download CSV",
                csv_data,
                file_name=f"enhanced_search_{query}_{datetime.now().strftime('%Y%m%d_%H%M')}.csv",
                mime="text/csv",
                key="enhanced_export_csv"
            )
        
        with col2:
            json_data = df.to_json(orient='records', indent=2)
            st.download_button(
                "📋 Download JSON",
                json_data,
                file_name=f"enhanced_search_{query}_{datetime.now().strftime('%Y%m%d_%H%M')}.json",
                mime="application/json",
                key="enhanced_export_json"
            )
        
        with col3:
            # Create summary report
            summary_report = f"""
# Enhanced Search Results Summary

**Query:** {query}
**Search Type:** {search_type}
**Total Results:** {analysis.get('total_results', 0)}
**Unique Domains:** {analysis.get('unique_domains', 0)}
**Average Sentiment:** {analysis.get('avg_sentiment', 0):.3f}

## Top Domains:
{chr(10).join([f"- {domain}: {count} results" for domain, count in list(analysis.get('top_domains', {}).items())[:5]])}

## Sentiment Distribution:
{chr(10).join([f"- {sentiment}: {count} results" for sentiment, count in analysis.get('sentiment_distribution', {}).items()])}

## Top Keywords:
{chr(10).join([f"- {keyword}: {count} mentions" for keyword, count in list(analysis.get('top_keywords', {}).items())[:10]])}
            """
            
            st.download_button(
                "📊 Download Report",
                summary_report,
                file_name=f"enhanced_search_report_{query}_{datetime.now().strftime('%Y%m%d_%H%M')}.md",
                mime="text/markdown",
                key="enhanced_export_report"
            )
    
    # Search history
    if search_engine.search_history:
        st.subheader("📚 Search History")
        history_df = pd.DataFrame(search_engine.search_history)
        st.dataframe(history_df, use_container_width=True, hide_index=True)
