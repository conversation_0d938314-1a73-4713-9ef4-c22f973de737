"""
Simple Job Search Fallback
==========================

A simple, reliable job search using requests and BeautifulSoup
for when Crawl4AI fails or returns irrelevant results.
"""

import requests
from bs4 import BeautifulSoup
import re
import time
import random
from urllib.parse import quote_plus
from typing import List, Dict
import streamlit as st

class SimpleJobSearcher:
    """Simple job searcher using requests and BeautifulSoup"""
    
    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    
    def search_vietnamworks(self, keyword: str, location: str = "") -> List[Dict]:
        """Search VietnamWorks using simple requests"""
        jobs = []
        
        try:
            # Build search URL
            base_url = "https://www.vietnamworks.com/search"
            params = {
                'keyword': keyword,
                'location': location if location else '',
                'page': 1
            }
            
            # Make request
            response = requests.get(base_url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find job listings with multiple possible selectors
            job_elements = soup.find_all(['div', 'article'], class_=re.compile(r'job|search-result|listing'))
            
            for element in job_elements[:10]:  # Limit to 10 jobs
                try:
                    job = self._extract_job_info(element, 'vietnamworks', 'https://www.vietnamworks.com')
                    if job and self._is_relevant(job, keyword, location):
                        jobs.append(job)
                except Exception:
                    continue
            
        except Exception as e:
            print(f"Error searching VietnamWorks: {e}")
        
        return jobs
    
    def search_topcv(self, keyword: str, location: str = "") -> List[Dict]:
        """Search TopCV using simple requests"""
        jobs = []
        
        try:
            # Build search URL
            base_url = "https://www.topcv.vn/viec-lam"
            params = {
                'keyword': keyword,
                'location': location if location else ''
            }
            
            # Make request
            response = requests.get(base_url, params=params, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find job listings
            job_elements = soup.find_all(['div', 'article'], class_=re.compile(r'job|search|listing'))
            
            for element in job_elements[:10]:  # Limit to 10 jobs
                try:
                    job = self._extract_job_info(element, 'topcv', 'https://www.topcv.vn')
                    if job and self._is_relevant(job, keyword, location):
                        jobs.append(job)
                except Exception:
                    continue
            
        except Exception as e:
            print(f"Error searching TopCV: {e}")
        
        return jobs
    
    def improve_job_search(self, keyword: str, location: str = "") -> List[Dict]:
        """Improved job search with better site coverage"""

        jobs = []

        # Try multiple job sites with different approaches
        job_sites = [
            {
                'name': 'VietnamWorks',
                'url': 'https://www.vietnamworks.com/search',
                'params': {'keyword': keyword, 'location': location}
            },
            {
                'name': 'TopCV',
                'url': 'https://www.topcv.vn/viec-lam',
                'params': {'keyword': keyword, 'location': location}
            },
            {
                'name': 'CareerBuilder',
                'url': 'https://careerbuilder.vn/viec-lam',
                'params': {'keywords': keyword, 'location': location}
            }
        ]

        for site in job_sites:
            try:
                response = requests.get(
                    site['url'],
                    params=site['params'],
                    headers=self.headers,
                    timeout=10
                )

                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    site_jobs = self._extract_jobs_from_site(soup, site['name'])
                    jobs.extend(site_jobs)

                time.sleep(1)  # Be respectful to servers

            except Exception as e:
                print(f"Error searching {site['name']}: {e}")
                continue

        return jobs

    def _extract_jobs_from_site(self, soup, site_name: str) -> List[Dict]:
        """Extract jobs from a specific site's HTML"""

        jobs = []

        # Common job listing selectors
        job_selectors = [
            'div[class*="job"]',
            'article[class*="job"]',
            'div[class*="search-result"]',
            'div[class*="listing"]',
            '.job-item',
            '.search-item',
            '.job-card'
        ]

        for selector in job_selectors:
            elements = soup.select(selector)
            if elements:
                for element in elements[:5]:  # Limit per selector
                    job = self._extract_job_info(element, site_name.lower(), '')
                    if job and job.get('title') and job.get('company'):
                        jobs.append(job)
                break  # Use first successful selector

        return jobs
    
    def _extract_job_info(self, element, source: str, base_url: str) -> Dict:
        """Extract job information from HTML element"""
        
        # Try to find title
        title_selectors = ['h1', 'h2', 'h3', 'h4', '.title', '.job-title', '.job-name', 'a[title]']
        title = ""
        for selector in title_selectors:
            title_elem = element.select_one(selector)
            if title_elem:
                title = title_elem.get_text(strip=True)
                if title:
                    break
        
        # Try to find company
        company_selectors = ['.company', '.company-name', '.employer', '.company-link']
        company = ""
        for selector in company_selectors:
            company_elem = element.select_one(selector)
            if company_elem:
                company = company_elem.get_text(strip=True)
                if company:
                    break
        
        # Try to find location
        location_selectors = ['.location', '.address', '.job-location', '.work-location']
        location = ""
        for selector in location_selectors:
            location_elem = element.select_one(selector)
            if location_elem:
                location = location_elem.get_text(strip=True)
                if location:
                    break
        
        # Try to find salary
        salary_selectors = ['.salary', '.wage', '.salary-range', '.job-salary']
        salary = ""
        for selector in salary_selectors:
            salary_elem = element.select_one(selector)
            if salary_elem:
                salary = salary_elem.get_text(strip=True)
                if salary:
                    break
        
        # Try to find URL
        url = ""
        link_elem = element.find('a', href=True)
        if link_elem:
            url = link_elem['href']
            if url and not url.startswith('http'):
                url = base_url + url
        
        # Get description
        description = element.get_text(strip=True)[:200]
        
        return {
            'title': title,
            'company': company,
            'location': location,
            'salary': salary or 'Not specified',
            'description': description,
            'url': url,
            'source': source,
            'crawled_at': time.strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def _is_relevant(self, job: Dict, keyword: str, location: str = "") -> bool:
        """Check if job is relevant to search criteria"""
        
        # Combine job text
        job_text = " ".join([
            job.get('title', ''),
            job.get('company', ''),
            job.get('description', '')
        ]).lower()
        
        # Check keyword relevance
        keyword_words = keyword.lower().split()
        keyword_matches = sum(1 for word in keyword_words if len(word) > 2 and word in job_text)
        
        # Must have at least one keyword match
        if keyword_matches == 0:
            return False
        
        # Check location if specified
        if location:
            job_location = job.get('location', '').lower()
            location_words = location.lower().split()
            location_matches = any(word in job_location for word in location_words if len(word) > 2)
            
            # If location specified but no match, still accept if job has no location
            if not location_matches and job_location.strip():
                return False
        
        return True
    
    def search_all(self, keyword: str, location: str = "") -> List[Dict]:
        """Search all sources and return combined results"""

        all_jobs = []

        # Try improved job search
        try:
            improved_jobs = self.improve_job_search(keyword, location)
            all_jobs.extend(improved_jobs)
        except Exception as e:
            print(f"Error in improved search: {e}")

        # Try original methods as fallback
        if len(all_jobs) < 3:  # If we have fewer than 3 jobs, try original methods
            try:
                vietnamworks_jobs = self.search_vietnamworks(keyword, location)
                all_jobs.extend(vietnamworks_jobs)
                time.sleep(1)
            except Exception:
                pass

            try:
                topcv_jobs = self.search_topcv(keyword, location)
                all_jobs.extend(topcv_jobs)
                time.sleep(1)
            except Exception:
                pass

        # If still no jobs found, show helpful message
        if len(all_jobs) == 0:
            st.warning("⚠️ No jobs found from job sites. This could be due to:")
            st.info("• Job sites blocking automated requests\n• Network connectivity issues\n• Search terms too specific")
            st.info("💡 Try different keywords or check the job sites directly")
            return []
        
        # Remove duplicates
        unique_jobs = []
        seen_titles = set()
        
        for job in all_jobs:
            title_key = f"{job.get('title', '').lower()}_{job.get('company', '').lower()}"
            if title_key not in seen_titles:
                seen_titles.add(title_key)
                unique_jobs.append(job)
        
        return unique_jobs

# Global simple searcher
simple_searcher = SimpleJobSearcher()

def simple_job_search(keyword: str, location: str = "") -> List[Dict]:
    """Simple job search function"""
    return simple_searcher.search_all(keyword, location)
