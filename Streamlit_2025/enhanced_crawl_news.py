"""
Enhanced News Crawler with Multiple Sources
==========================================

Integrates multiple news sources with user selection options:
1. <PERSON><PERSON><PERSON> (thoibaotaichinhvietnam.vn)
2. VnEconomy (vneconomy.vn)
"""

import asyncio
import json
import streamlit as st
import pandas as pd
from datetime import datetime
from typing import List, Dict, Optional
from urllib.parse import urljoin

# Import existing crawlers
from crawl_news import fin_news_extract
from vneconomy_crawler_standalone import VnEconomyNewsCrawler

class EnhancedNewsCrawler:
    """Enhanced news crawler supporting multiple sources"""
    
    def __init__(self):
        self.sources = {
            "thoibaotaichinh": {
                "name": "Thời Báo Tài Chính Việt Nam",
                "url": "https://thoibaotaichinhvietnam.vn/",
                "description": "Financial news and market analysis"
            },
            "vneconomy": {
                "name": "VnEconomy",
                "url": "https://vneconomy.vn/chung-khoan.htm",
                "description": "Economic and stock market news"
            }
        }
    
    async def crawl_thoibaotaichinh(self, topic_filter: str = "") -> List[Dict]:
        """Crawl Thoi Bao Tai Chinh Vietnam using existing function with optional topic filtering"""
        try:
            articles = await fin_news_extract()

            # Standardize the format
            standardized_articles = []
            for article in articles:
                # Safely extract article data with None handling
                title = article.get("TITLE") or ""
                link = article.get("LINK") or ""
                time_val = article.get("TIME") or ""
                summary = article.get("SUMMARY") or ""
                content = article.get("CONTENT") or ""

                standardized_article = {
                    "title": str(title),
                    "link": str(link),
                    "time": str(time_val),
                    "summary": str(summary),
                    "content": str(content),
                    "source": "Thời Báo Tài Chính Việt Nam",
                    "source_url": "https://thoibaotaichinhvietnam.vn/",
                    "crawled_at": datetime.now().isoformat()
                }

                # Apply topic filter if specified
                if topic_filter:
                    # Safely get text fields and handle None values
                    title_text = (standardized_article.get("title") or "").lower()
                    summary_text = (standardized_article.get("summary") or "").lower()
                    content_text = (standardized_article.get("content") or "").lower()
                    topic_lower = topic_filter.lower()

                    if (topic_lower in title_text or
                        topic_lower in summary_text or
                        topic_lower in content_text):
                        standardized_articles.append(standardized_article)
                else:
                    standardized_articles.append(standardized_article)

            return standardized_articles

        except Exception as e:
            st.error(f"Error crawling Thoi Bao Tai Chinh: {str(e)}")
            return []
    
    async def crawl_vneconomy(self, max_pages: int = 2, topic_filter: str = "") -> List[Dict]:
        """Crawl VnEconomy using the standalone crawler with optional topic filtering"""
        try:
            crawler = VnEconomyNewsCrawler(headless=True)
            articles = await crawler.crawl_all_pages(max_pages=max_pages)

            # Standardize the format
            standardized_articles = []
            for article in articles:
                # Safely extract article data with None handling
                title = article.get("title") or ""
                link = article.get("url") or ""
                time_val = article.get("time") or ""

                standardized_article = {
                    "title": str(title),
                    "link": str(link),
                    "time": str(time_val),
                    "summary": "",  # VnEconomy doesn't provide summary in the list
                    "content": "",  # Would need additional crawling for full content
                    "source": "VnEconomy",
                    "source_url": "https://vneconomy.vn/",
                    "crawled_at": datetime.now().isoformat()
                }

                # Apply topic filter if specified
                if topic_filter:
                    # Safely get text fields and handle None values
                    title_text = (standardized_article.get("title") or "").lower()
                    topic_lower = topic_filter.lower()

                    if topic_lower in title_text:
                        standardized_articles.append(standardized_article)
                else:
                    standardized_articles.append(standardized_article)

            return standardized_articles

        except Exception as e:
            st.error(f"Error crawling VnEconomy: {str(e)}")
            return []
    
    async def crawl_both_sources(self, vneconomy_pages: int = 2, topic_filter: str = "") -> Dict[str, List[Dict]]:
        """Crawl both sources simultaneously with optional topic filtering"""
        try:
            # Run both crawlers concurrently
            thoibaotaichinh_task = self.crawl_thoibaotaichinh(topic_filter=topic_filter)
            vneconomy_task = self.crawl_vneconomy(max_pages=vneconomy_pages, topic_filter=topic_filter)

            thoibaotaichinh_articles, vneconomy_articles = await asyncio.gather(
                thoibaotaichinh_task,
                vneconomy_task,
                return_exceptions=True
            )

            # Handle exceptions
            if isinstance(thoibaotaichinh_articles, Exception):
                st.error(f"Thoi Bao Tai Chinh crawling failed: {str(thoibaotaichinh_articles)}")
                thoibaotaichinh_articles = []

            if isinstance(vneconomy_articles, Exception):
                st.error(f"VnEconomy crawling failed: {str(vneconomy_articles)}")
                vneconomy_articles = []

            return {
                "thoibaotaichinh": thoibaotaichinh_articles,
                "vneconomy": vneconomy_articles
            }

        except Exception as e:
            st.error(f"Error crawling both sources: {str(e)}")
            return {"thoibaotaichinh": [], "vneconomy": []}

def create_enhanced_news_interface():
    """Create the enhanced news crawler interface"""

    st.title("📰 Enhanced News Crawler")
    st.markdown("**Crawl financial and economic news from multiple Vietnamese sources with topic filtering**")

    # Initialize session state
    if "news_data" not in st.session_state:
        st.session_state.news_data = None
    if "crawl_results" not in st.session_state:
        st.session_state.crawl_results = {}

    # Initialize crawler
    crawler = EnhancedNewsCrawler()

    # Main configuration section (no sidebar duplication)
    st.subheader("🔧 Crawler Configuration")

    # Configuration in main area
    col1, col2 = st.columns(2)

    with col1:
        st.write("**📰 News Sources**")
        source_option = st.radio(
            "Select news source(s):",
            [
                "🏢 Thời Báo Tài Chính Việt Nam",
                "📈 VnEconomy",
                "🔄 Both Sources"
            ],
            help="Choose which news source(s) to crawl",
            key="source_selection"
        )

    with col2:
        st.write("**🔍 Topic Search & Options**")

        # Topic search input
        topic_filter = st.text_input(
            "Search for specific topic:",
            placeholder="e.g., chứng khoán, ngân hàng, bất động sản",
            help="Enter keywords to filter articles by topic (leave empty for all articles)",
            key="topic_search"
        )

        # VnEconomy specific options
        if source_option in ["📈 VnEconomy", "🔄 Both Sources"]:
            vneconomy_pages = st.slider(
                "VnEconomy pages to crawl:",
                min_value=1,
                max_value=5,
                value=2,
                help="More pages = more articles but slower crawling",
                key="vneconomy_pages"
            )
        else:
            vneconomy_pages = 2
    
    # Display source information
    st.subheader("📊 Available News Sources")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.info(
            "**🏢 Thời Báo Tài Chính Việt Nam**\n\n"
            "• URL: https://thoibaotaichinhvietnam.vn/\n"
            "• Focus: Financial news and market analysis\n"
            "• Content: Full article content included\n"
            "• Language: Vietnamese"
        )
    
    with col2:
        st.info(
            "**📈 VnEconomy**\n\n"
            "• URL: https://vneconomy.vn/chung-khoan.htm\n"
            "• Focus: Economic and stock market news\n"
            "• Content: Headlines and summaries\n"
            "• Language: Vietnamese"
        )
    
    # Single unified crawling section
    st.subheader("🚀 Start Crawling")

    # Display current configuration
    config_info = f"**Source:** {source_option}"
    if topic_filter:
        config_info += f" | **Topic:** '{topic_filter}'"
    if source_option in ["📈 VnEconomy", "🔄 Both Sources"]:
        config_info += f" | **VnEconomy Pages:** {vneconomy_pages}"

    st.info(config_info)

    # Single crawl button based on selection
    col1, col2 = st.columns([1, 3])

    with col1:
        crawl_button_text = {
            "🏢 Thời Báo Tài Chính Việt Nam": "🏢 Start Crawling",
            "📈 VnEconomy": "📈 Start Crawling",
            "🔄 Both Sources": "🔄 Start Crawling"
        }

        if st.button(crawl_button_text[source_option], type="primary", key="unified_crawl_btn"):
            # Execute crawling based on selection
            if source_option == "🏢 Thời Báo Tài Chính Việt Nam":
                with st.spinner("Crawling Thời Báo Tài Chính Việt Nam..."):
                    articles = asyncio.run(crawler.crawl_thoibaotaichinh(topic_filter=topic_filter))
                    if articles:
                        st.session_state.crawl_results = {"thoibaotaichinh": articles}
                        if topic_filter:
                            st.success(f"✅ Found {len(articles)} articles about '{topic_filter}' from Thời Báo!")
                        else:
                            st.success(f"✅ Found {len(articles)} articles from Thời Báo!")
                    else:
                        if topic_filter:
                            st.warning(f"No articles found about '{topic_filter}' from Thời Báo")
                        else:
                            st.warning("No articles found from Thời Báo")

            elif source_option == "📈 VnEconomy":
                with st.spinner(f"Crawling VnEconomy ({vneconomy_pages} pages)..."):
                    articles = asyncio.run(crawler.crawl_vneconomy(max_pages=vneconomy_pages, topic_filter=topic_filter))
                    if articles:
                        st.session_state.crawl_results = {"vneconomy": articles}
                        if topic_filter:
                            st.success(f"✅ Found {len(articles)} articles about '{topic_filter}' from VnEconomy!")
                        else:
                            st.success(f"✅ Found {len(articles)} articles from VnEconomy!")
                    else:
                        if topic_filter:
                            st.warning(f"No articles found about '{topic_filter}' from VnEconomy")
                        else:
                            st.warning("No articles found from VnEconomy")

            elif source_option == "🔄 Both Sources":
                with st.spinner("Crawling both sources simultaneously..."):
                    results = asyncio.run(crawler.crawl_both_sources(vneconomy_pages=vneconomy_pages, topic_filter=topic_filter))

                    total_articles = 0
                    st.session_state.crawl_results = {}
                    for source, articles in results.items():
                        if articles:
                            st.session_state.crawl_results[source] = articles
                            total_articles += len(articles)

                    if total_articles > 0:
                        if topic_filter:
                            st.success(f"✅ Found {total_articles} articles about '{topic_filter}' from both sources!")
                        else:
                            st.success(f"✅ Found {total_articles} total articles from both sources!")
                        st.balloons()
                    else:
                        if topic_filter:
                            st.warning(f"No articles found about '{topic_filter}' from either source")
                        else:
                            st.warning("No articles found from either source")

    with col2:
        if topic_filter:
            st.write("**💡 Topic Search Tips:**")
            st.write("• Use Vietnamese keywords for better results")
            st.write("• Try broader terms if no results found")
            st.write("• Examples: 'chứng khoán', 'ngân hàng', 'kinh tế'")
        else:
            st.write("**ℹ️ Crawling Info:**")
            st.write("• No topic filter - will get all available articles")
            st.write("• Add a topic above to filter results")
            st.write("• Concurrent crawling for faster results")
    
    # Display results
    if st.session_state.crawl_results:
        st.divider()
        st.subheader("📊 Crawling Results")
        
        # Summary metrics
        col1, col2, col3 = st.columns(3)
        
        thoibaotaichinh_count = len(st.session_state.crawl_results.get("thoibaotaichinh", []))
        vneconomy_count = len(st.session_state.crawl_results.get("vneconomy", []))
        total_count = thoibaotaichinh_count + vneconomy_count
        
        with col1:
            st.metric("🏢 Thời Báo Articles", thoibaotaichinh_count)
        with col2:
            st.metric("📈 VnEconomy Articles", vneconomy_count)
        with col3:
            st.metric("📊 Total Articles", total_count)
        
        # Results display tabs
        if thoibaotaichinh_count > 0 and vneconomy_count > 0:
            tab1, tab2, tab3 = st.tabs(["🏢 Thời Báo", "📈 VnEconomy", "🔄 Combined"])
        elif thoibaotaichinh_count > 0:
            tab1, tab3 = st.tabs(["🏢 Thời Báo", "🔄 All Results"])
            tab2 = None
        elif vneconomy_count > 0:
            tab2, tab3 = st.tabs(["📈 VnEconomy", "🔄 All Results"])
            tab1 = None
        else:
            tab3 = st.container()
            tab1 = tab2 = None
        
        # Display Thoi Bao results
        if tab1 and thoibaotaichinh_count > 0:
            with tab1:
                header_text = f"**{thoibaotaichinh_count} articles from Thời Báo Tài Chính Việt Nam**"
                if topic_filter:
                    header_text += f" (filtered by: '{topic_filter}')"
                st.write(header_text)
                df_thoibaotaichinh = pd.DataFrame(st.session_state.crawl_results["thoibaotaichinh"])
                st.dataframe(df_thoibaotaichinh, use_container_width=True, hide_index=True)

        # Display VnEconomy results
        if tab2 and vneconomy_count > 0:
            with tab2:
                header_text = f"**{vneconomy_count} articles from VnEconomy**"
                if topic_filter:
                    header_text += f" (filtered by: '{topic_filter}')"
                st.write(header_text)
                df_vneconomy = pd.DataFrame(st.session_state.crawl_results["vneconomy"])
                st.dataframe(df_vneconomy, use_container_width=True, hide_index=True)

        # Display combined results
        with tab3:
            if total_count > 0:
                header_text = f"**{total_count} total articles from all sources**"
                if topic_filter:
                    header_text += f" (filtered by: '{topic_filter}')"
                st.write(header_text)

                # Combine all articles
                all_articles = []
                for source, articles in st.session_state.crawl_results.items():
                    all_articles.extend(articles)

                # Sort by crawled time (most recent first)
                all_articles.sort(key=lambda x: x.get("crawled_at", ""), reverse=True)

                df_combined = pd.DataFrame(all_articles)
                st.dataframe(df_combined, use_container_width=True, hide_index=True)

                # Store combined data for export
                st.session_state.news_data = all_articles
            else:
                st.info("No articles to display. Try crawling some sources first!")
        
        # Export options
        if total_count > 0:
            st.subheader("📥 Export Options")
            
            col1, col2, col3 = st.columns(3)
            
            # Prepare combined data for export
            all_articles = []
            for source, articles in st.session_state.crawl_results.items():
                all_articles.extend(articles)
            
            df_export = pd.DataFrame(all_articles)
            
            with col1:
                csv_data = df_export.to_csv(index=False)
                st.download_button(
                    "📄 Download CSV",
                    csv_data,
                    file_name=f"news_articles_{datetime.now().strftime('%Y%m%d_%H%M')}.csv",
                    mime="text/csv"
                )
            
            with col2:
                json_data = df_export.to_json(orient='records', indent=2)
                st.download_button(
                    "📋 Download JSON",
                    json_data,
                    file_name=f"news_articles_{datetime.now().strftime('%Y%m%d_%H%M')}.json",
                    mime="application/json"
                )
            
            with col3:
                # Create summary report
                topic_info = f"**Topic Filter:** '{topic_filter}'" if topic_filter else "**Topic Filter:** None (all articles)"

                summary_report = f"""
# News Crawling Summary Report

**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Total Articles:** {total_count}
{topic_info}

## Sources:
- 🏢 Thời Báo Tài Chính Việt Nam: {thoibaotaichinh_count} articles
- 📈 VnEconomy: {vneconomy_count} articles

## Recent Headlines:
{chr(10).join([f"- {article['title'][:100]}..." if len(article['title']) > 100 else f"- {article['title']}" for article in all_articles[:10]])}

## Search Configuration:
- Source Selection: {source_option}
- Topic Filter: {topic_filter if topic_filter else 'None'}
- VnEconomy Pages: {vneconomy_pages if source_option in ["📈 VnEconomy", "🔄 Both Sources"] else 'N/A'}
                """

                file_suffix = f"_{topic_filter.replace(' ', '_')}" if topic_filter else ""
                st.download_button(
                    "📊 Download Report",
                    summary_report,
                    file_name=f"news_summary{file_suffix}_{datetime.now().strftime('%Y%m%d_%H%M')}.md",
                    mime="text/markdown"
                )
        
        # Clear results button
        if st.button("🗑️ Clear Results"):
            st.session_state.crawl_results = {}
            st.session_state.news_data = None
            st.rerun()

# Backward compatibility function
def run_enhanced_extraction():
    """Run extraction with enhanced crawler for backward compatibility"""
    crawler = EnhancedNewsCrawler()
    return asyncio.run(crawler.crawl_thoibaotaichinh())
