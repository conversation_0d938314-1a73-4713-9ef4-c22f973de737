#!/usr/bin/env python3
"""
Test script to verify that all Streamlit element keys are unique
"""

import re
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def extract_streamlit_keys(file_path):
    """Extract all Streamlit element keys from a Python file"""
    keys = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find all key= patterns
        key_patterns = [
            r'key\s*=\s*["\']([^"\']+)["\']',  # key="value" or key='value'
            r'key\s*=\s*f["\']([^"\']+)["\']',  # key=f"value" or key=f'value'
        ]
        
        for pattern in key_patterns:
            matches = re.findall(pattern, content)
            keys.extend(matches)
        
        return keys
        
    except Exception as e:
        print(f"Error reading {file_path}: {str(e)}")
        return []

def check_key_uniqueness(file_path):
    """Check if all keys in a file are unique"""
    keys = extract_streamlit_keys(file_path)
    
    if not keys:
        return True, [], []
    
    # Find duplicates
    seen = set()
    duplicates = set()
    
    for key in keys:
        if key in seen:
            duplicates.add(key)
        else:
            seen.add(key)
    
    return len(duplicates) == 0, list(seen), list(duplicates)

def test_enhanced_search_keys():
    """Test the enhanced search file for key conflicts"""
    print("🧪 Testing Enhanced Search Key Uniqueness...")
    
    file_path = "enhanced_search.py"
    
    if not os.path.exists(file_path):
        print(f"❌ File {file_path} not found")
        return
    
    is_unique, all_keys, duplicates = check_key_uniqueness(file_path)
    
    print(f"📊 Analysis Results for {file_path}:")
    print(f"   - Total keys found: {len(all_keys)}")
    print(f"   - Unique keys: {is_unique}")
    
    if all_keys:
        print(f"   - All keys: {sorted(all_keys)}")
    
    if duplicates:
        print(f"❌ Duplicate keys found: {duplicates}")
        return False
    else:
        print("✅ All keys are unique!")
        return True

def test_app_keys():
    """Test the main app file for key conflicts"""
    print("\n🧪 Testing Main App Key Uniqueness...")
    
    file_path = "app.py"
    
    if not os.path.exists(file_path):
        print(f"❌ File {file_path} not found")
        return
    
    is_unique, all_keys, duplicates = check_key_uniqueness(file_path)
    
    print(f"📊 Analysis Results for {file_path}:")
    print(f"   - Total keys found: {len(all_keys)}")
    print(f"   - Unique keys: {is_unique}")
    
    if duplicates:
        print(f"❌ Duplicate keys found: {duplicates}")
        return False
    else:
        print("✅ All keys are unique!")
        return True

def test_specific_key_patterns():
    """Test for specific key patterns that were causing issues"""
    print("\n🧪 Testing Specific Key Patterns...")
    
    file_path = "enhanced_search.py"
    
    if not os.path.exists(file_path):
        print(f"❌ File {file_path} not found")
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for the specific problematic patterns
        problematic_patterns = [
            r'key\s*=\s*f?["\']comp_query_\d+["\']',  # comp_query_0, comp_query_1, etc.
            r'key\s*=\s*["\']compare\d+["\']',        # compare1, compare2
        ]
        
        issues_found = []
        
        for pattern in problematic_patterns:
            matches = re.findall(pattern, content)
            if matches:
                issues_found.extend(matches)
        
        if issues_found:
            print(f"❌ Found problematic key patterns: {issues_found}")
            return False
        else:
            print("✅ No problematic key patterns found!")
            
        # Check for the new fixed patterns
        fixed_patterns = [
            r'key\s*=\s*["\']competitive_query_\d+["\']',  # competitive_query_0, etc.
            r'key\s*=\s*["\']history_compare\d+["\']',     # history_compare1, etc.
            r'key\s*=\s*["\']trend_[a-z_]+["\']',          # trend_query_input, etc.
            r'key\s*=\s*["\']export_[a-z_]+["\']',         # export_report_type, etc.
        ]
        
        fixed_found = []
        for pattern in fixed_patterns:
            matches = re.findall(pattern, content)
            fixed_found.extend(matches)
        
        if fixed_found:
            print(f"✅ Found fixed key patterns: {sorted(set(fixed_found))}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking patterns: {str(e)}")
        return False

def test_key_categories():
    """Test that keys are properly categorized by function"""
    print("\n🧪 Testing Key Categorization...")
    
    file_path = "enhanced_search.py"
    
    if not os.path.exists(file_path):
        print(f"❌ File {file_path} not found")
        return
    
    keys = extract_streamlit_keys(file_path)
    
    # Categorize keys by prefix
    categories = {
        "competitive_": [],
        "trend_": [],
        "export_": [],
        "history_": [],
        "other": []
    }
    
    for key in keys:
        categorized = False
        for prefix in categories.keys():
            if prefix != "other" and key.startswith(prefix):
                categories[prefix].append(key)
                categorized = True
                break
        
        if not categorized:
            categories["other"].append(key)
    
    print("📊 Key Categories:")
    for category, key_list in categories.items():
        if key_list:
            print(f"   - {category}: {len(key_list)} keys")
            for key in sorted(key_list):
                print(f"     • {key}")
    
    return True

def main():
    """Run all key conflict tests"""
    print("🚀 Testing Streamlit Key Uniqueness...\n")
    
    # Test individual files
    enhanced_search_ok = test_enhanced_search_keys()
    app_ok = test_app_keys()
    
    # Test specific patterns
    patterns_ok = test_specific_key_patterns()
    
    # Test categorization
    test_key_categories()
    
    print("\n🎉 Key Conflict Testing Completed!")
    
    if enhanced_search_ok and app_ok and patterns_ok:
        print("\n✅ All tests passed! No key conflicts detected.")
        print("\n📋 Summary of Fixes Applied:")
        print("✅ Changed 'comp_query_X' to 'competitive_query_X'")
        print("✅ Changed 'compare1/compare2' to 'history_compare1/history_compare2'")
        print("✅ Added unique keys for trend analysis tools")
        print("✅ Added unique keys for export and reporting tools")
        print("✅ Added unique keys for all buttons and form elements")
        
        print("\n🚀 Your enhanced search feature should now work without key conflicts!")
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
    
    print("\n💡 Key Naming Convention Used:")
    print("- competitive_*: For competitive analysis tools")
    print("- trend_*: For trend analysis tools")
    print("- export_*: For export and reporting tools")
    print("- history_*: For search history and comparison")
    print("- *_btn: For all button elements")

if __name__ == "__main__":
    main()
