#!/usr/bin/env python3
"""
Test script for the enhanced stock analysis features
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_market_analysis():
    """Test market analysis functions"""
    print("🧪 Testing Market Analysis...")
    
    try:
        from market_analysis import (
            get_stock_performance_category,
            VIETNAMESE_SECTORS,
            fetch_market_data_parallel
        )
        
        # Test performance categorization
        category = get_stock_performance_category(
            current_price=100,
            previous_close=95,
            ceiling_price=101.65,
            floor_price=88.35
        )
        print(f"✅ Performance category test: {category}")
        
        # Test sector mapping
        print(f"✅ Sectors loaded: {len(VIETNAMESE_SECTORS)} sectors")
        
        # Test with a small sample of symbols
        test_symbols = ['VCB', 'VHM', 'VNM']
        print(f"✅ Testing with symbols: {test_symbols}")
        
        print("✅ Market analysis functions loaded successfully!")
        
    except Exception as e:
        print(f"❌ Market analysis test failed: {str(e)}")

def test_prediction_models():
    """Test prediction model functions"""
    print("\n🧪 Testing Prediction Models...")
    
    try:
        from prediction_models import StockPredictionModel
        
        # Create sample data
        dates = pd.date_range(start='2023-01-01', end='2024-01-01', freq='D')
        np.random.seed(42)
        
        sample_data = pd.DataFrame({
            'time': dates,
            'open': 100 + np.random.randn(len(dates)).cumsum() * 0.5,
            'high': 100 + np.random.randn(len(dates)).cumsum() * 0.5 + 2,
            'low': 100 + np.random.randn(len(dates)).cumsum() * 0.5 - 2,
            'close': 100 + np.random.randn(len(dates)).cumsum() * 0.5,
            'volume': np.random.randint(1000000, 10000000, len(dates))
        })
        
        # Test model initialization
        model = StockPredictionModel()
        print("✅ Model initialized successfully!")
        
        # Test feature creation
        features_df = model.create_technical_features(sample_data)
        print(f"✅ Technical features created: {features_df.shape[1]} features")
        
        # Test target creation
        target_df = model.create_target_variable(features_df)
        print(f"✅ Target variable created: {len(target_df)} samples")
        
        print("✅ Prediction models loaded successfully!")
        
    except Exception as e:
        print(f"❌ Prediction models test failed: {str(e)}")

def test_sentiment_analysis():
    """Test sentiment analysis functions"""
    print("\n🧪 Testing Sentiment Analysis...")
    
    try:
        from stock_analysis import analyze_news_sentiment
        
        # Test with sample news
        sample_news = [
            "Company reports strong quarterly earnings growth",
            "Stock price hits new 52-week high",
            "Market volatility concerns investors",
            "Positive outlook for next quarter"
        ]
        
        sentiment_result = analyze_news_sentiment(sample_news)
        print(f"✅ Sentiment analysis result: {sentiment_result['sentiment_label']}")
        print(f"   Score: {sentiment_result['sentiment_score']:.3f}")
        print(f"   Positive: {sentiment_result['positive_count']}")
        print(f"   Negative: {sentiment_result['negative_count']}")
        print(f"   Neutral: {sentiment_result['neutral_count']}")
        
        print("✅ Sentiment analysis working successfully!")
        
    except Exception as e:
        print(f"❌ Sentiment analysis test failed: {str(e)}")

def test_enhanced_visualizations():
    """Test enhanced visualization functions"""
    print("\n🧪 Testing Enhanced Visualizations...")
    
    try:
        from stock_analysis import create_enhanced_price_chart
        
        # Create sample data
        dates = pd.date_range(start='2023-01-01', end='2024-01-01', freq='D')
        np.random.seed(42)
        
        sample_data = pd.DataFrame({
            'time': dates,
            'open': 100 + np.random.randn(len(dates)).cumsum() * 0.5,
            'high': 100 + np.random.randn(len(dates)).cumsum() * 0.5 + 2,
            'low': 100 + np.random.randn(len(dates)).cumsum() * 0.5 - 2,
            'close': 100 + np.random.randn(len(dates)).cumsum() * 0.5,
            'volume': np.random.randint(1000000, 10000000, len(dates))
        })
        
        print("✅ Sample data created for visualization test")
        print("✅ Enhanced visualization functions loaded successfully!")
        
    except Exception as e:
        print(f"❌ Enhanced visualizations test failed: {str(e)}")

def test_dependencies():
    """Test if all required dependencies are available"""
    print("\n🧪 Testing Dependencies...")
    
    required_packages = [
        'streamlit',
        'pandas',
        'numpy',
        'plotly',
        'sklearn',
        'vnstock',
        'textblob'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sklearn':
                import sklearn
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Please install missing packages using: pip install -r requirements.txt")
    else:
        print("\n✅ All required dependencies are available!")

def main():
    """Run all tests"""
    print("🚀 Starting Enhanced Stock Analysis Tests...\n")
    
    # Test dependencies first
    test_dependencies()
    
    # Test individual components
    test_market_analysis()
    test_prediction_models()
    test_sentiment_analysis()
    test_enhanced_visualizations()
    
    print("\n🎉 Testing completed!")
    print("\n📋 Summary:")
    print("- Market Analysis: Enhanced with sector breakdown and performance categories")
    print("- AI Predictions: Machine learning models for price forecasting")
    print("- Sentiment Analysis: News sentiment integration")
    print("- Visualizations: Interactive charts with technical indicators")
    print("\n🚀 Your enhanced stock analysis platform is ready!")

if __name__ == "__main__":
    main()
