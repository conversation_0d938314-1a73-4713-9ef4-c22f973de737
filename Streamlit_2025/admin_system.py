"""
Admin System with User Behavior Tracking and Support Features
============================================================

This module provides:
1. Admin authentication (sign-in only, no sign-up)
2. User behavior and engagement tracking
3. User support chat system
4. Admin dashboard with analytics
"""

import streamlit as st
import pandas as pd
import json
import sqlite3
import hashlib
from datetime import datetime, timedelta
import time
import uuid
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, List, Optional, Any
import os
from pathlib import Path

class UserTracker:
    """Tracks user behavior and engagement metrics"""
    
    def __init__(self, db_path: str = "user_analytics.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the user analytics database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # User sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_sessions (
                session_id TEXT PRIMARY KEY,
                user_id TEXT,
                username TEXT,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                duration_seconds INTEGER,
                browser_type TEXT,
                user_agent TEXT,
                ip_address TEXT,
                device_type TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Page visits table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS page_visits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                page_name TEXT,
                visit_time TIMESTAMP,
                duration_seconds INTEGER,
                interactions_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES user_sessions (session_id)
            )
        ''')
        
        # User interactions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_interactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT,
                page_name TEXT,
                interaction_type TEXT,
                element_name TEXT,
                interaction_time TIMESTAMP,
                details TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES user_sessions (session_id)
            )
        ''')
        
        # Support tickets table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS support_tickets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ticket_id TEXT UNIQUE,
                user_id TEXT,
                username TEXT,
                subject TEXT,
                message TEXT,
                status TEXT DEFAULT 'open',
                priority TEXT DEFAULT 'medium',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                admin_response TEXT,
                admin_id TEXT
            )
        ''')
        
        # Chat messages table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS chat_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ticket_id TEXT,
                sender_type TEXT,
                sender_id TEXT,
                message TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (ticket_id) REFERENCES support_tickets (ticket_id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def start_session(self, user_id: str, username: str, browser_info: Dict) -> str:
        """Start a new user session"""
        session_id = str(uuid.uuid4())
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO user_sessions 
            (session_id, user_id, username, start_time, browser_type, user_agent, device_type)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            session_id,
            user_id,
            username,
            datetime.now(),
            browser_info.get('browser', 'Unknown'),
            browser_info.get('user_agent', 'Unknown'),
            browser_info.get('device', 'Unknown')
        ))
        
        conn.commit()
        conn.close()
        
        return session_id
    
    def end_session(self, session_id: str):
        """End a user session"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get session start time
        cursor.execute('SELECT start_time FROM user_sessions WHERE session_id = ?', (session_id,))
        result = cursor.fetchone()
        
        if result:
            start_time = datetime.fromisoformat(result[0])
            duration = (datetime.now() - start_time).total_seconds()
            
            cursor.execute('''
                UPDATE user_sessions 
                SET end_time = ?, duration_seconds = ?
                WHERE session_id = ?
            ''', (datetime.now(), int(duration), session_id))
        
        conn.commit()
        conn.close()
    
    def track_page_visit(self, session_id: str, page_name: str):
        """Track a page visit"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO page_visits (session_id, page_name, visit_time)
            VALUES (?, ?, ?)
        ''', (session_id, page_name, datetime.now()))
        
        conn.commit()
        conn.close()
    
    def track_interaction(self, session_id: str, page_name: str, 
                         interaction_type: str, element_name: str, details: str = ""):
        """Track user interaction"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO user_interactions 
            (session_id, page_name, interaction_type, element_name, interaction_time, details)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (session_id, page_name, interaction_type, element_name, datetime.now(), details))
        
        conn.commit()
        conn.close()
    
    def get_analytics_data(self, days: int = 30) -> Dict[str, Any]:
        """Get comprehensive analytics data"""
        conn = sqlite3.connect(self.db_path)
        
        # Date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Total users and sessions
        total_users = pd.read_sql_query('''
            SELECT COUNT(DISTINCT user_id) as count
            FROM user_sessions 
            WHERE start_time >= ?
        ''', conn, params=(start_date,))
        
        total_sessions = pd.read_sql_query('''
            SELECT COUNT(*) as count
            FROM user_sessions 
            WHERE start_time >= ?
        ''', conn, params=(start_date,))
        
        # Average session duration
        avg_duration = pd.read_sql_query('''
            SELECT AVG(duration_seconds) as avg_duration
            FROM user_sessions 
            WHERE start_time >= ? AND duration_seconds IS NOT NULL
        ''', conn, params=(start_date,))
        
        # Daily active users
        daily_users = pd.read_sql_query('''
            SELECT DATE(start_time) as date, COUNT(DISTINCT user_id) as users
            FROM user_sessions 
            WHERE start_time >= ?
            GROUP BY DATE(start_time)
            ORDER BY date
        ''', conn, params=(start_date,))
        
        # Browser distribution
        browser_stats = pd.read_sql_query('''
            SELECT browser_type, COUNT(*) as count
            FROM user_sessions 
            WHERE start_time >= ?
            GROUP BY browser_type
        ''', conn, params=(start_date,))
        
        # Page popularity
        page_stats = pd.read_sql_query('''
            SELECT page_name, COUNT(*) as visits
            FROM page_visits 
            WHERE visit_time >= ?
            GROUP BY page_name
            ORDER BY visits DESC
        ''', conn, params=(start_date,))
        
        # User activity by hour
        hourly_activity = pd.read_sql_query('''
            SELECT strftime('%H', start_time) as hour, COUNT(*) as sessions
            FROM user_sessions 
            WHERE start_time >= ?
            GROUP BY strftime('%H', start_time)
            ORDER BY hour
        ''', conn, params=(start_date,))
        
        # Most active users
        active_users = pd.read_sql_query('''
            SELECT username, COUNT(*) as sessions, 
                   AVG(duration_seconds) as avg_duration
            FROM user_sessions 
            WHERE start_time >= ?
            GROUP BY username
            ORDER BY sessions DESC
            LIMIT 10
        ''', conn, params=(start_date,))
        
        conn.close()
        
        return {
            'total_users': total_users.iloc[0]['count'] if not total_users.empty else 0,
            'total_sessions': total_sessions.iloc[0]['count'] if not total_sessions.empty else 0,
            'avg_duration': avg_duration.iloc[0]['avg_duration'] if not avg_duration.empty else 0,
            'daily_users': daily_users,
            'browser_stats': browser_stats,
            'page_stats': page_stats,
            'hourly_activity': hourly_activity,
            'active_users': active_users
        }

class SupportSystem:
    """Handles user support tickets and chat"""
    
    def __init__(self, db_path: str = "user_analytics.db"):
        self.db_path = db_path
    
    def create_ticket(self, user_id: str, username: str, subject: str, message: str) -> str:
        """Create a new support ticket"""
        ticket_id = f"TICKET-{int(time.time())}-{str(uuid.uuid4())[:8]}"
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO support_tickets 
            (ticket_id, user_id, username, subject, message)
            VALUES (?, ?, ?, ?, ?)
        ''', (ticket_id, user_id, username, subject, message))
        
        # Add initial message to chat
        cursor.execute('''
            INSERT INTO chat_messages (ticket_id, sender_type, sender_id, message)
            VALUES (?, ?, ?, ?)
        ''', (ticket_id, 'user', user_id, message))
        
        conn.commit()
        conn.close()
        
        return ticket_id
    
    def add_chat_message(self, ticket_id: str, sender_type: str, sender_id: str, message: str):
        """Add a message to ticket chat"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO chat_messages (ticket_id, sender_type, sender_id, message)
            VALUES (?, ?, ?, ?)
        ''', (ticket_id, sender_type, sender_id, message))
        
        # Update ticket timestamp
        cursor.execute('''
            UPDATE support_tickets 
            SET updated_at = CURRENT_TIMESTAMP
            WHERE ticket_id = ?
        ''', (ticket_id,))
        
        conn.commit()
        conn.close()
    
    def get_user_tickets(self, user_id: str) -> pd.DataFrame:
        """Get all tickets for a user"""
        conn = sqlite3.connect(self.db_path)
        
        tickets = pd.read_sql_query('''
            SELECT * FROM support_tickets 
            WHERE user_id = ?
            ORDER BY created_at DESC
        ''', conn, params=(user_id,))
        
        conn.close()
        return tickets
    
    def get_all_tickets(self, status: str = None) -> pd.DataFrame:
        """Get all support tickets (admin view)"""
        conn = sqlite3.connect(self.db_path)
        
        query = 'SELECT * FROM support_tickets'
        params = ()
        
        if status:
            query += ' WHERE status = ?'
            params = (status,)
        
        query += ' ORDER BY created_at DESC'
        
        tickets = pd.read_sql_query(query, conn, params=params)
        conn.close()
        return tickets
    
    def get_ticket_chat(self, ticket_id: str) -> pd.DataFrame:
        """Get chat messages for a ticket"""
        conn = sqlite3.connect(self.db_path)
        
        messages = pd.read_sql_query('''
            SELECT * FROM chat_messages 
            WHERE ticket_id = ?
            ORDER BY timestamp ASC
        ''', conn, params=(ticket_id,))
        
        conn.close()
        return messages
    
    def update_ticket_status(self, ticket_id: str, status: str, admin_id: str = None):
        """Update ticket status"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE support_tickets 
            SET status = ?, updated_at = CURRENT_TIMESTAMP, admin_id = ?
            WHERE ticket_id = ?
        ''', (status, admin_id, ticket_id))
        
        conn.commit()
        conn.close()

class AdminAuth:
    """Admin authentication system"""
    
    @staticmethod
    def verify_admin_credentials(username: str, password: str) -> bool:
        """Verify admin credentials"""
        # For demo purposes - in production, use proper password hashing
        admin_credentials = {
            "admin": "admin123",
            "superadmin": "super456"
        }
        
        return admin_credentials.get(username) == password
    
    @staticmethod
    def is_admin_user(username: str) -> bool:
        """Check if user has admin privileges"""
        if not username:
            return False

        # For streamlit-authenticator 0.4.2 compatibility
        # Admin users are determined by username
        admin_usernames = ['admin']  # Add more admin usernames here if needed

        return username in admin_usernames

# Initialize global instances
user_tracker = UserTracker()
support_system = SupportSystem()

def create_admin_dashboard():
    """Create the admin dashboard interface"""

    st.title("🔧 Admin Dashboard")
    st.markdown("**Comprehensive system administration and user analytics**")

    # Check admin permissions
    if not AdminAuth.is_admin_user(st.session_state.get('username', '')):
        st.error("❌ Access Denied: Admin privileges required")
        return

    # Admin navigation
    admin_tabs = st.tabs([
        "📊 Analytics",
        "👥 User Management",
        "🎫 Support Tickets",
        "💬 Live Chat",
        "⚙️ System Settings"
    ])

    with admin_tabs[0]:
        create_analytics_dashboard()

    with admin_tabs[1]:
        create_user_management_dashboard()

    with admin_tabs[2]:
        create_support_dashboard()

    with admin_tabs[3]:
        create_live_chat_dashboard()

    with admin_tabs[4]:
        create_system_settings_dashboard()

def create_analytics_dashboard():
    """Create user analytics dashboard"""
    st.subheader("📊 User Behavior & Engagement Analytics")

    # Time range selector
    col1, col2 = st.columns(2)
    with col1:
        days = st.selectbox("Time Range", [7, 14, 30, 60, 90], index=2)
    with col2:
        if st.button("🔄 Refresh Data"):
            st.rerun()

    # Get analytics data
    analytics = user_tracker.get_analytics_data(days=days)

    # Key metrics
    st.subheader("📈 Key Metrics")
    metric_cols = st.columns(4)

    with metric_cols[0]:
        st.metric(
            "Total Users",
            analytics['total_users'],
            help="Unique users in selected period"
        )

    with metric_cols[1]:
        st.metric(
            "Total Sessions",
            analytics['total_sessions'],
            help="Total user sessions"
        )

    with metric_cols[2]:
        avg_duration = analytics['avg_duration']
        if avg_duration and pd.notna(avg_duration) and avg_duration > 0:
            duration_str = f"{int(avg_duration // 60)}m {int(avg_duration % 60)}s"
        else:
            duration_str = "0m 0s"
        st.metric(
            "Avg Session Duration",
            duration_str,
            help="Average time users spend per session"
        )

    with metric_cols[3]:
        sessions_per_user = analytics['total_sessions'] / max(analytics['total_users'], 1)
        st.metric(
            "Sessions per User",
            f"{sessions_per_user:.1f}",
            help="Average sessions per unique user"
        )

    # Charts
    chart_cols = st.columns(2)

    with chart_cols[0]:
        st.subheader("📅 Daily Active Users")
        if not analytics['daily_users'].empty:
            try:
                # Create chart with error handling
                import plotly.graph_objects as go

                fig_daily = go.Figure()
                fig_daily.add_trace(go.Scatter(
                    x=analytics['daily_users']['date'],
                    y=analytics['daily_users']['users'],
                    mode='lines+markers',
                    name='Daily Users',
                    line=dict(color='#667eea', width=3),
                    marker=dict(size=6)
                ))

                fig_daily.update_layout(
                    title="Daily Active Users Trend",
                    height=400,
                    xaxis_title="Date",
                    yaxis_title="Users",
                    showlegend=False
                )

                st.plotly_chart(fig_daily, use_container_width=True)
            except Exception as e:
                st.error(f"Chart error: {str(e)}")
                st.dataframe(analytics['daily_users'], use_container_width=True)
        else:
            st.info("No daily user data available")

    with chart_cols[1]:
        st.subheader("🌐 Browser Distribution")
        if not analytics['browser_stats'].empty:
            try:
                import plotly.graph_objects as go

                fig_browser = go.Figure(data=[go.Pie(
                    labels=analytics['browser_stats']['browser_type'],
                    values=analytics['browser_stats']['count'],
                    hole=0.3
                )])

                fig_browser.update_layout(
                    title="Browser Usage Distribution",
                    height=400,
                    showlegend=True
                )

                st.plotly_chart(fig_browser, use_container_width=True)
            except Exception as e:
                st.error(f"Chart error: {str(e)}")
                st.dataframe(analytics['browser_stats'], use_container_width=True)
        else:
            st.info("No browser data available")

    # Page popularity
    st.subheader("📄 Page Popularity")
    if not analytics['page_stats'].empty:
        fig_pages = px.bar(
            analytics['page_stats'].head(10),
            x='visits',
            y='page_name',
            orientation='h',
            title="Most Visited Pages"
        )
        fig_pages.update_layout(height=400)
        st.plotly_chart(fig_pages, use_container_width=True)
    else:
        st.info("No page visit data available")

    # Hourly activity
    st.subheader("🕐 User Activity by Hour")
    if not analytics['hourly_activity'].empty:
        fig_hourly = px.bar(
            analytics['hourly_activity'],
            x='hour',
            y='sessions',
            title="User Activity Throughout the Day"
        )
        fig_hourly.update_layout(height=400)
        st.plotly_chart(fig_hourly, use_container_width=True)
    else:
        st.info("No hourly activity data available")

    # Most active users
    st.subheader("👑 Most Active Users")
    if not analytics['active_users'].empty:
        active_users_display = analytics['active_users'].copy()
        active_users_display['avg_duration'] = active_users_display['avg_duration'].apply(
            lambda x: f"{int(x // 60)}m {int(x % 60)}s" if pd.notna(x) else "0m 0s"
        )
        st.dataframe(
            active_users_display,
            column_config={
                "username": "Username",
                "sessions": "Sessions",
                "avg_duration": "Avg Duration"
            },
            hide_index=True,
            use_container_width=True
        )
    else:
        st.info("No user activity data available")

def create_user_management_dashboard():
    """Create user management dashboard"""
    st.subheader("👥 User Management")

    # User statistics
    st.subheader("📊 User Statistics")

    try:
        import yaml
        with open('config.yaml', 'r') as file:
            config = yaml.safe_load(file)

        users = config.get('credentials', {}).get('usernames', {})

        # User overview
        total_users = len(users)
        # Use username-based admin detection (compatible with streamlit-authenticator 0.4.2)
        admin_usernames = ['admin']  # Add more admin usernames here if needed
        admin_users = sum(1 for username in users.keys() if username in admin_usernames)
        regular_users = total_users - admin_users

        user_cols = st.columns(3)
        with user_cols[0]:
            st.metric("Total Users", total_users)
        with user_cols[1]:
            st.metric("Admin Users", admin_users)
        with user_cols[2]:
            st.metric("Regular Users", regular_users)

        # User list
        st.subheader("👤 User List")

        user_data = []
        admin_usernames = ['admin']  # Add more admin usernames here if needed
        for username, user_info in users.items():
            user_data.append({
                'Username': username,
                'Name': user_info.get('name', ''),
                'Email': user_info.get('email', ''),
                'Role': 'Admin' if username in admin_usernames else 'User',
                'Account Status': 'Active'  # Simplified for streamlit-authenticator 0.4.2
            })

        if user_data:
            users_df = pd.DataFrame(user_data)
            st.dataframe(users_df, use_container_width=True, hide_index=True)
        else:
            st.info("No users found")

    except Exception as e:
        st.error(f"Error loading user data: {str(e)}")

def create_support_dashboard():
    """Create support tickets dashboard"""
    st.subheader("🎫 Support Tickets Management")

    # Ticket statistics
    all_tickets = support_system.get_all_tickets()

    if not all_tickets.empty:
        # Ticket metrics
        ticket_cols = st.columns(4)

        with ticket_cols[0]:
            st.metric("Total Tickets", len(all_tickets))
        with ticket_cols[1]:
            open_tickets = len(all_tickets[all_tickets['status'] == 'open'])
            st.metric("Open Tickets", open_tickets)
        with ticket_cols[2]:
            closed_tickets = len(all_tickets[all_tickets['status'] == 'closed'])
            st.metric("Closed Tickets", closed_tickets)
        with ticket_cols[3]:
            in_progress = len(all_tickets[all_tickets['status'] == 'in_progress'])
            st.metric("In Progress", in_progress)

        # Filter tickets
        status_filter = st.selectbox(
            "Filter by Status",
            ["All", "open", "in_progress", "closed"]
        )

        filtered_tickets = all_tickets if status_filter == "All" else all_tickets[all_tickets['status'] == status_filter]

        # Display tickets
        for _, ticket in filtered_tickets.iterrows():
            with st.expander(f"🎫 {ticket['ticket_id']} - {ticket['subject']} ({ticket['status']})"):
                col1, col2 = st.columns(2)

                with col1:
                    st.write(f"**User:** {ticket['username']}")
                    st.write(f"**Created:** {ticket['created_at']}")
                    st.write(f"**Status:** {ticket['status']}")

                with col2:
                    st.write(f"**Priority:** {ticket['priority']}")
                    st.write(f"**Updated:** {ticket['updated_at']}")

                st.write(f"**Message:** {ticket['message']}")

                # Admin actions
                action_cols = st.columns(3)
                with action_cols[0]:
                    if st.button(f"Mark In Progress", key=f"progress_{ticket['id']}"):
                        support_system.update_ticket_status(
                            ticket['ticket_id'],
                            'in_progress',
                            st.session_state.get('username')
                        )
                        st.rerun()

                with action_cols[1]:
                    if st.button(f"Mark Closed", key=f"close_{ticket['id']}"):
                        support_system.update_ticket_status(
                            ticket['ticket_id'],
                            'closed',
                            st.session_state.get('username')
                        )
                        st.rerun()

                with action_cols[2]:
                    if st.button(f"View Chat", key=f"chat_{ticket['id']}"):
                        st.session_state.selected_ticket = ticket['ticket_id']
                        st.rerun()
    else:
        st.info("No support tickets found")

def create_live_chat_dashboard():
    """Create live chat dashboard for admin"""
    st.subheader("💬 Live Chat Support")

    # Show selected ticket chat or ticket selection
    if 'selected_ticket' in st.session_state and st.session_state.selected_ticket:
        ticket_id = st.session_state.selected_ticket

        # Back button
        if st.button("← Back to Tickets"):
            del st.session_state.selected_ticket
            st.rerun()

        st.write(f"**Ticket:** {ticket_id}")

        # Display chat messages
        messages = support_system.get_ticket_chat(ticket_id)

        if not messages.empty:
            for _, msg in messages.iterrows():
                sender_icon = "👤" if msg['sender_type'] == 'user' else "🔧"
                sender_name = "User" if msg['sender_type'] == 'user' else "Admin"

                with st.chat_message(msg['sender_type']):
                    st.write(f"{sender_icon} **{sender_name}** - {msg['timestamp']}")
                    st.write(msg['message'])

        # Admin reply
        admin_reply = st.text_area("Reply to user:", key=f"reply_{ticket_id}")
        if st.button("Send Reply"):
            if admin_reply.strip():
                support_system.add_chat_message(
                    ticket_id,
                    'admin',
                    st.session_state.get('username'),
                    admin_reply
                )
                st.success("Reply sent!")
                st.rerun()
    else:
        st.info("Select a ticket from the Support Tickets tab to start chatting")

def create_system_settings_dashboard():
    """Create system settings dashboard"""
    st.subheader("⚙️ System Settings")

    # Database management
    st.subheader("🗄️ Database Management")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("📊 Export Analytics Data"):
            analytics = user_tracker.get_analytics_data(days=90)

            # Create export data
            export_data = {
                'summary': {
                    'total_users': analytics['total_users'],
                    'total_sessions': analytics['total_sessions'],
                    'avg_duration': analytics['avg_duration']
                },
                'daily_users': analytics['daily_users'].to_dict('records'),
                'browser_stats': analytics['browser_stats'].to_dict('records'),
                'page_stats': analytics['page_stats'].to_dict('records')
            }

            st.download_button(
                "📥 Download Analytics JSON",
                json.dumps(export_data, indent=2, default=str),
                file_name=f"analytics_export_{datetime.now().strftime('%Y%m%d_%H%M')}.json",
                mime="application/json"
            )

    with col2:
        if st.button("🎫 Export Support Tickets"):
            tickets = support_system.get_all_tickets()
            if not tickets.empty:
                csv_data = tickets.to_csv(index=False)
                st.download_button(
                    "📥 Download Tickets CSV",
                    csv_data,
                    file_name=f"support_tickets_{datetime.now().strftime('%Y%m%d_%H%M')}.csv",
                    mime="text/csv"
                )

    # System information
    st.subheader("ℹ️ System Information")

    info_cols = st.columns(2)

    with info_cols[0]:
        st.info(f"""
        **Database Path:** user_analytics.db
        **Config File:** config.yaml
        **Admin User:** {st.session_state.get('username', 'Unknown')}
        **Session Start:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """)

    with info_cols[1]:
        # Check database size
        try:
            db_size = os.path.getsize("user_analytics.db") / 1024 / 1024  # MB
            st.info(f"""
            **Database Size:** {db_size:.2f} MB
            **Streamlit Version:** {st.__version__}
            **Python Version:** {os.sys.version.split()[0]}
            """)
        except:
            st.info("Database information not available")
