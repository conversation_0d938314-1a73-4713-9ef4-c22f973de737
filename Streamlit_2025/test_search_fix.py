#!/usr/bin/env python3
"""
Test script to verify the enhanced search fix
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_search_import():
    """Test that the enhanced search can be imported without errors"""
    print("🧪 Testing Enhanced Search Import...")
    
    try:
        from enhanced_search_fixed import EnhancedSearchEngine, create_advanced_search_interface
        print("✅ Enhanced search modules imported successfully")
        
        # Test class initialization
        search_engine = EnhancedSearchEngine()
        print("✅ EnhancedSearchEngine initialized successfully")
        
        # Test sentiment analysis
        sentiment_positive = search_engine._get_sentiment_label(0.5)
        sentiment_negative = search_engine._get_sentiment_label(-0.5)
        sentiment_neutral = search_engine._get_sentiment_label(0.05)
        
        print(f"✅ Sentiment analysis working: Positive={sentiment_positive}, Negative={sentiment_negative}, Neutral={sentiment_neutral}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced search import failed: {str(e)}")
        return False

def test_dependencies():
    """Test that all required dependencies are available"""
    print("\n🧪 Testing Dependencies...")
    
    required_modules = [
        'streamlit',
        'pandas',
        'plotly.express',
        'textblob',
        'general_search'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - MISSING")
            missing_modules.append(module)
    
    return len(missing_modules) == 0

def test_sample_data_processing():
    """Test data processing with sample data"""
    print("\n🧪 Testing Data Processing...")
    
    try:
        from enhanced_search_fixed import EnhancedSearchEngine
        
        search_engine = EnhancedSearchEngine()
        
        # Sample search results
        sample_results = [
            {
                "title": "AI Technology Breakthrough",
                "url": "https://example.com/ai-news",
                "description": "Amazing new AI technology shows promising results",
                "domain": "example.com",
                "sentiment_score": 0.8,
                "sentiment_label": "Positive",
                "search_type": "general",
                "timestamp": "2024-01-01T12:00:00"
            },
            {
                "title": "Market Analysis Report",
                "url": "https://news.com/market-report",
                "description": "Comprehensive analysis of current market trends",
                "domain": "news.com",
                "sentiment_score": 0.1,
                "sentiment_label": "Neutral",
                "search_type": "general",
                "timestamp": "2024-01-01T13:00:00"
            }
        ]
        
        # Test analysis
        analysis = search_engine.analyze_search_results(sample_results)
        
        print(f"✅ Analysis completed:")
        print(f"   - Total results: {analysis.get('total_results', 0)}")
        print(f"   - Unique domains: {analysis.get('unique_domains', 0)}")
        print(f"   - Average sentiment: {analysis.get('avg_sentiment', 0):.3f}")
        print(f"   - Top domains: {analysis.get('top_domains', {})}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data processing test failed: {str(e)}")
        return False

def test_app_integration():
    """Test integration with the main app"""
    print("\n🧪 Testing App Integration...")
    
    try:
        # Test that the app can import the fixed module
        from enhanced_search_fixed import create_advanced_search_interface
        
        print("✅ App can import enhanced search interface")
        
        # Test that the function exists and is callable
        if callable(create_advanced_search_interface):
            print("✅ create_advanced_search_interface is callable")
        else:
            print("❌ create_advanced_search_interface is not callable")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ App integration test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Enhanced Search Fix...\n")
    
    # Run tests
    import_ok = test_enhanced_search_import()
    deps_ok = test_dependencies()
    data_ok = test_sample_data_processing()
    app_ok = test_app_integration()
    
    print("\n🎉 Enhanced Search Fix Testing Completed!")
    
    if import_ok and deps_ok and data_ok and app_ok:
        print("\n✅ All tests passed! The enhanced search fix is working correctly.")
        print("\n📋 Summary of Fixes:")
        print("✅ Removed complex tab-based interface that caused key conflicts")
        print("✅ Simplified to single interface with unique keys")
        print("✅ Fixed crawl4ai timeout issues by using basic search")
        print("✅ Added proper sentiment analysis with TextBlob")
        print("✅ Maintained all analytics and visualization features")
        print("✅ Added comprehensive export capabilities")
        
        print("\n🚀 Your enhanced search feature should now work without errors!")
        print("\n💡 How to use:")
        print("1. Go to General Search in your Streamlit app")
        print("2. Choose 'AI-Powered Search' mode")
        print("3. Enter your search query")
        print("4. Click 'Start Enhanced Search'")
        print("5. View results with sentiment analysis and visualizations")
        print("6. Export results in multiple formats")
        
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
        
        if not import_ok:
            print("- Fix import issues in enhanced_search_fixed.py")
        if not deps_ok:
            print("- Install missing dependencies")
        if not data_ok:
            print("- Check data processing functions")
        if not app_ok:
            print("- Check app integration")

if __name__ == "__main__":
    main()
