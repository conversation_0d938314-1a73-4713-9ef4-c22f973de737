"""
User Support Chat Widget
========================

Provides a floating chat widget for user support that appears on all pages
"""

import streamlit as st
import time
from datetime import datetime
from admin_system import support_system, user_tracker

def create_support_chat_widget():
    """Create floating support chat widget"""
    
    # Initialize chat state
    if 'chat_open' not in st.session_state:
        st.session_state.chat_open = False
    if 'current_ticket' not in st.session_state:
        st.session_state.current_ticket = None
    if 'chat_messages' not in st.session_state:
        st.session_state.chat_messages = []
    
    # CSS for floating chat widget
    st.markdown("""
    <style>
    .chat-widget {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        font-family: 'Source Sans Pro', sans-serif;
    }
    
    .chat-button {
        background: #ff6b6b;
        color: white;
        border: none;
        border-radius: 50px;
        padding: 15px 20px;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
        box-shadow: 0 2px 8px rgba(255,107,107,0.3);
        transition: all 0.3s ease;
    }
    
    .chat-button:hover {
        background: #ff5252;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255,107,107,0.4);
    }
    
    .chat-window {
        width: 350px;
        height: 500px;
        background: white;
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }
    
    .chat-header {
        background: #ff6b6b;
        color: white;
        padding: 15px;
        font-weight: bold;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .chat-close {
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .chat-messages {
        flex: 1;
        padding: 15px;
        overflow-y: auto;
        max-height: 300px;
    }
    
    .chat-input-area {
        padding: 15px;
        border-top: 1px solid #eee;
    }
    
    .message {
        margin-bottom: 10px;
        padding: 8px 12px;
        border-radius: 8px;
        max-width: 80%;
    }
    
    .message.user {
        background: #e3f2fd;
        margin-left: auto;
        text-align: right;
    }
    
    .message.admin {
        background: #f5f5f5;
        margin-right: auto;
    }
    
    .message-time {
        font-size: 11px;
        color: #666;
        margin-top: 4px;
    }
    
    .quick-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 15px;
    }
    
    .quick-action-btn {
        background: #f0f0f0;
        border: 1px solid #ddd;
        border-radius: 15px;
        padding: 6px 12px;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .quick-action-btn:hover {
        background: #e0e0e0;
        border-color: #ccc;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # Chat widget container
    chat_container = st.container()
    
    with chat_container:
        if not st.session_state.chat_open:
            # Chat button
            if st.button("💬 Need Help?", key="chat_toggle", help="Click to open support chat"):
                st.session_state.chat_open = True
                st.rerun()
        else:
            # Chat window
            create_chat_window()

def create_chat_window():
    """Create the main chat window interface"""
    
    # Chat window header
    col1, col2 = st.columns([4, 1])
    with col1:
        st.markdown("### 💬 Support Chat")
    with col2:
        if st.button("✕", key="chat_close", help="Close chat"):
            st.session_state.chat_open = False
            st.rerun()
    
    # Check if user is logged in
    if not st.session_state.get('authentication_status'):
        st.warning("Please log in to use support chat")
        return
    
    username = st.session_state.get('username', 'Anonymous')
    user_id = st.session_state.get('username', 'anonymous')
    
    # Quick action buttons
    st.markdown("**Quick Actions:**")
    quick_actions = st.columns(3)
    
    with quick_actions[0]:
        if st.button("🐛 Report Bug", key="quick_bug"):
            st.session_state.quick_message = "I found a bug in the application. Here are the details:"
    
    with quick_actions[1]:
        if st.button("❓ Ask Question", key="quick_question"):
            st.session_state.quick_message = "I have a question about how to use:"
    
    with quick_actions[2]:
        if st.button("💡 Feature Request", key="quick_feature"):
            st.session_state.quick_message = "I would like to suggest a new feature:"
    
    # Display existing ticket or create new one
    if st.session_state.current_ticket:
        display_existing_chat()
    else:
        create_new_ticket_form()

def create_new_ticket_form():
    """Form to create a new support ticket"""
    
    st.markdown("**Start a new conversation:**")
    
    # Subject input
    subject = st.text_input(
        "Subject:", 
        placeholder="Brief description of your issue",
        key="ticket_subject"
    )
    
    # Message input with quick message pre-fill
    default_message = st.session_state.get('quick_message', '')
    if default_message:
        del st.session_state.quick_message
    
    message = st.text_area(
        "Message:", 
        value=default_message,
        placeholder="Describe your issue or question in detail...",
        height=100,
        key="ticket_message"
    )
    
    # Priority selection
    priority = st.selectbox(
        "Priority:",
        ["low", "medium", "high", "urgent"],
        index=1,
        key="ticket_priority"
    )
    
    # Submit button
    if st.button("🚀 Start Chat", key="create_ticket"):
        if subject.strip() and message.strip():
            # Create new ticket
            ticket_id = support_system.create_ticket(
                user_id=st.session_state.get('username', 'anonymous'),
                username=st.session_state.get('username', 'Anonymous'),
                subject=subject,
                message=message
            )
            
            st.session_state.current_ticket = ticket_id
            st.session_state.chat_messages = []
            st.success("✅ Support ticket created! An admin will respond soon.")
            
            # Track interaction
            if 'session_id' in st.session_state:
                user_tracker.track_interaction(
                    st.session_state.session_id,
                    "Support Chat",
                    "ticket_created",
                    "new_ticket",
                    f"Subject: {subject}, Priority: {priority}"
                )
            
            time.sleep(1)
            st.rerun()
        else:
            st.error("Please fill in both subject and message")

def display_existing_chat():
    """Display existing chat conversation"""
    
    ticket_id = st.session_state.current_ticket
    
    # Chat header with ticket info
    st.markdown(f"**Ticket:** `{ticket_id}`")
    
    # Load and display messages
    messages = support_system.get_ticket_chat(ticket_id)
    
    if not messages.empty:
        # Messages container
        messages_container = st.container()
        
        with messages_container:
            for _, msg in messages.iterrows():
                sender_type = msg['sender_type']
                sender_name = "You" if sender_type == 'user' else "Support"
                timestamp = msg['timestamp']
                content = msg['message']
                
                # Message bubble
                if sender_type == 'user':
                    st.markdown(f"""
                    <div style="text-align: right; margin-bottom: 10px;">
                        <div style="display: inline-block; background: #e3f2fd; padding: 8px 12px; border-radius: 8px; max-width: 80%;">
                            <strong>{sender_name}</strong><br>
                            {content}
                            <div style="font-size: 11px; color: #666; margin-top: 4px;">{timestamp}</div>
                        </div>
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.markdown(f"""
                    <div style="text-align: left; margin-bottom: 10px;">
                        <div style="display: inline-block; background: #f5f5f5; padding: 8px 12px; border-radius: 8px; max-width: 80%;">
                            <strong>🔧 {sender_name}</strong><br>
                            {content}
                            <div style="font-size: 11px; color: #666; margin-top: 4px;">{timestamp}</div>
                        </div>
                    </div>
                    """, unsafe_allow_html=True)
    
    # Message input
    st.markdown("---")
    new_message = st.text_area(
        "Type your message:",
        placeholder="Type your reply here...",
        height=80,
        key="new_chat_message"
    )
    
    # Send button and actions
    chat_actions = st.columns([2, 1, 1])
    
    with chat_actions[0]:
        if st.button("📤 Send Message", key="send_message"):
            if new_message.strip():
                support_system.add_chat_message(
                    ticket_id,
                    'user',
                    st.session_state.get('username', 'anonymous'),
                    new_message
                )
                
                # Track interaction
                if 'session_id' in st.session_state:
                    user_tracker.track_interaction(
                        st.session_state.session_id,
                        "Support Chat",
                        "message_sent",
                        "chat_message",
                        f"Ticket: {ticket_id}"
                    )
                
                st.success("Message sent!")
                time.sleep(1)
                st.rerun()
            else:
                st.error("Please enter a message")
    
    with chat_actions[1]:
        if st.button("🔄 Refresh", key="refresh_chat"):
            st.rerun()
    
    with chat_actions[2]:
        if st.button("🏁 End Chat", key="end_chat"):
            st.session_state.current_ticket = None
            st.session_state.chat_messages = []
            st.success("Chat ended. You can start a new one anytime!")
            time.sleep(1)
            st.rerun()

def get_user_browser_info():
    """Get user browser information for tracking"""
    # This is a simplified version - in a real app, you'd use JavaScript
    # to get actual browser information
    return {
        'browser': 'Unknown',
        'user_agent': 'Streamlit App',
        'device': 'Desktop'
    }

def initialize_user_session():
    """Initialize user session tracking"""
    if 'session_id' not in st.session_state and st.session_state.get('authentication_status'):
        browser_info = get_user_browser_info()
        session_id = user_tracker.start_session(
            user_id=st.session_state.get('username', 'anonymous'),
            username=st.session_state.get('username', 'Anonymous'),
            browser_info=browser_info
        )
        st.session_state.session_id = session_id

def track_page_visit(page_name: str):
    """Track page visit for analytics"""
    if 'session_id' in st.session_state:
        user_tracker.track_page_visit(st.session_state.session_id, page_name)

def track_user_interaction(interaction_type: str, element_name: str, details: str = ""):
    """Track user interaction for analytics"""
    if 'session_id' in st.session_state:
        # Get current page from session state or URL
        current_page = st.session_state.get('current_page', 'Unknown')
        user_tracker.track_interaction(
            st.session_state.session_id,
            current_page,
            interaction_type,
            element_name,
            details
        )
