"""
Job Crawling System for Vietnam Market
=====================================

This module crawls job postings from various Vietnamese job sites
using Crawl4AI and provides structured job data.
"""

import asyncio
import json
import re
from datetime import datetime
from typing import List, Dict, Optional
from urllib.parse import quote_plus, urljoin
import streamlit as st

from crawl4ai import AsyncWebCrawler, CacheMode, CrawlerRunConfig
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy

class VietnamJobCrawler:
    """Job crawler for Vietnamese job posting sites"""
    
    def __init__(self):
        self.job_sites = {
            'vietnamworks': {
                'base_url': 'https://www.vietnamworks.com',
                'search_url': 'https://www.vietnamworks.com/search?keyword={keyword}&location={location}',
                'schema': {
                    "name": "JobListings",
                    "baseSelector": ".job-item, .job-card, [data-job-id], .job-list-item, .search-result-item",
                    "fields": [
                        {"name": "title", "selector": ".job-title, h3, .title, .job-name, a[title]", "type": "text"},
                        {"name": "company", "selector": ".company-name, .company, .company-link, .employer", "type": "text"},
                        {"name": "location", "selector": ".location, .job-location, .address, .work-location", "type": "text"},
                        {"name": "salary", "selector": ".salary, .job-salary, .wage, .salary-range", "type": "text"},
                        {"name": "url", "selector": "a", "type": "attribute", "attribute": "href"},
                        {"name": "description", "selector": ".job-description, .description, .job-summary", "type": "text"}
                    ]
                }
            },
            'topcv': {
                'base_url': 'https://www.topcv.vn',
                'search_url': 'https://www.topcv.vn/viec-lam?keyword={keyword}&location={location}',
                'schema': {
                    "name": "JobListings",
                    "baseSelector": ".job-item, .job-card, .job-list-item, .job-ta, .job-item-search-result",
                    "fields": [
                        {"name": "title", "selector": ".job-title, h3, .title, .job-name, .job-title-search", "type": "text"},
                        {"name": "company", "selector": ".company-name, .company, .company-name-label, .company-link", "type": "text"},
                        {"name": "location", "selector": ".location, .address, .job-location, .location-job", "type": "text"},
                        {"name": "salary", "selector": ".salary, .wage, .salary-label, .job-salary", "type": "text"},
                        {"name": "url", "selector": "a", "type": "attribute", "attribute": "href"},
                        {"name": "description", "selector": ".job-description, .description, .job-summary", "type": "text"}
                    ]
                }
            },
            'careerbuilder': {
                'base_url': 'https://careerbuilder.vn',
                'search_url': 'https://careerbuilder.vn/tim-viec-lam/{keyword}',
                'schema': {
                    "name": "JobListings",
                    "baseSelector": ".job-item, .job-card, .cb-job-card",
                    "fields": [
                        {"name": "title", "selector": ".job-title, h3, .title", "type": "text"},
                        {"name": "company", "selector": ".company-name, .company", "type": "text"},
                        {"name": "location", "selector": ".location, .job-location", "type": "text"},
                        {"name": "salary", "selector": ".salary, .job-salary", "type": "text"},
                        {"name": "url", "selector": "a", "type": "attribute", "attribute": "href"},
                        {"name": "description", "selector": ".job-description, .description", "type": "text"}
                    ]
                }
            },
            'jobsgo': {
                'base_url': 'https://jobsgo.vn',
                'search_url': 'https://jobsgo.vn/tim-viec-lam?q={keyword}',
                'schema': {
                    "name": "JobListings",
                    "baseSelector": ".job-item, .job-card, .job-listing",
                    "fields": [
                        {"name": "title", "selector": ".job-title, h3, .title", "type": "text"},
                        {"name": "company", "selector": ".company-name, .company", "type": "text"},
                        {"name": "location", "selector": ".location, .address", "type": "text"},
                        {"name": "salary", "selector": ".salary, .wage", "type": "text"},
                        {"name": "url", "selector": "a", "type": "attribute", "attribute": "href"},
                        {"name": "description", "selector": ".job-description, .description", "type": "text"}
                    ]
                }
            }
        }
    
    async def crawl_job_site(self, site_name: str, keyword: str, location: str = "", 
                           max_pages: int = 2) -> List[Dict]:
        """Crawl jobs from a specific site"""
        
        if site_name not in self.job_sites:
            return []
        
        site_config = self.job_sites[site_name]
        jobs = []
        
        # Prepare search query
        search_keyword = f"{keyword} {location}".strip()
        encoded_keyword = quote_plus(search_keyword)
        
        async with AsyncWebCrawler(
            headless=True,
            verbose=False,
            user_agent_mode="random"
        ) as crawler:
            
            for page in range(max_pages):
                try:
                    # Build search URL
                    if '{keyword}' in site_config['search_url'] and '{location}' in site_config['search_url']:
                        search_url = site_config['search_url'].format(
                            keyword=quote_plus(keyword),
                            location=quote_plus(location) if location else ""
                        )
                    elif '{keyword}' in site_config['search_url']:
                        search_url = site_config['search_url'].format(keyword=encoded_keyword)
                    else:
                        search_url = site_config['search_url'] + f"?q={encoded_keyword}"
                        if location:
                            search_url += f"&location={quote_plus(location)}"
                    
                    # Add page parameter if not first page
                    if page > 0:
                        page_param = f"&page={page + 1}" if '?' in search_url else f"?page={page + 1}"
                        search_url += page_param
                    
                    # Configure crawler
                    extraction_strategy = JsonCssExtractionStrategy(site_config['schema'])
                    
                    config = CrawlerRunConfig(
                        cache_mode=CacheMode.BYPASS,
                        extraction_strategy=extraction_strategy,
                        delay_before_return_html=2,
                        screenshot=False,
                        page_timeout=20000,
                        js_code="""
                            async () => {
                                // Wait for page load
                                await new Promise(r => setTimeout(r, 2000));
                                
                                // Scroll to load more jobs
                                for (let i = 0; i < 3; i++) {
                                    window.scrollTo(0, document.body.scrollHeight);
                                    await new Promise(r => setTimeout(r, 1000));
                                }
                                
                                // Scroll back to top
                                window.scrollTo(0, 0);
                                await new Promise(r => setTimeout(r, 500));
                            }
                        """
                    )
                    
                    # Crawl the page
                    result = await crawler.arun(url=search_url, config=config)
                    
                    if result.success and result.extracted_content:
                        try:
                            page_jobs = json.loads(result.extracted_content)
                            
                            # Process and clean job data
                            for job in page_jobs:
                                cleaned_job = self._clean_job_data(job, site_name, site_config['base_url'])
                                if cleaned_job and cleaned_job['title']:  # Only add jobs with titles
                                    # Check relevance to search keywords
                                    if self._is_job_relevant(cleaned_job, keyword, location):
                                        jobs.append(cleaned_job)
                        
                        except json.JSONDecodeError:
                            print(f"Failed to parse JSON from {site_name} page {page + 1}")
                            continue
                    
                except Exception as e:
                    print(f"Error crawling {site_name} page {page + 1}: {e}")
                    continue
        
        return jobs
    
    def _clean_job_data(self, job: Dict, site_name: str, base_url: str) -> Dict:
        """Clean and standardize job data"""
        
        # Clean and validate URL
        url = job.get('url', '')
        if url and not url.startswith('http'):
            if url.startswith('/'):
                url = base_url + url
            else:
                url = base_url + '/' + url
        
        # Clean text fields
        title = self._clean_text(job.get('title', ''))
        company = self._clean_text(job.get('company', ''))
        location = self._clean_text(job.get('location', ''))
        salary = self._clean_text(job.get('salary', ''))
        description = self._clean_text(job.get('description', ''))
        
        return {
            'title': title,
            'company': company,
            'location': location,
            'salary': salary,
            'description': description,
            'url': url,
            'source': site_name,
            'crawled_at': datetime.now().isoformat()
        }
    
    def _clean_text(self, text: str) -> str:
        """Clean text content"""
        if not text:
            return ""

        # Remove extra whitespace and newlines
        text = re.sub(r'\s+', ' ', text.strip())

        # Remove common unwanted characters
        text = re.sub(r'[^\w\s\-.,()/:@]', '', text)

        return text[:500]  # Limit length

    def _is_job_relevant(self, job: Dict, search_keywords: str, location: str = "") -> bool:
        """Check if job is relevant to search keywords"""
        if not search_keywords:
            return True

        # Combine job text for relevance checking
        job_text = " ".join([
            job.get('title', ''),
            job.get('company', ''),
            job.get('description', ''),
            job.get('location', '')
        ]).lower()

        # Split search keywords
        keywords = search_keywords.lower().split()

        # Check if at least one keyword appears in job text
        keyword_matches = 0
        for keyword in keywords:
            if len(keyword) > 2:  # Only check meaningful keywords
                if keyword in job_text:
                    keyword_matches += 1

        # Job is relevant if at least 30% of keywords match
        relevance_threshold = max(1, len(keywords) * 0.3)
        is_keyword_relevant = keyword_matches >= relevance_threshold

        # Check location relevance if specified
        is_location_relevant = True
        if location and location.strip():
            location_lower = location.lower()
            job_location = job.get('location', '').lower()

            # Check if location appears in job location
            location_keywords = location_lower.split()
            location_matches = any(loc_keyword in job_location for loc_keyword in location_keywords if len(loc_keyword) > 2)
            is_location_relevant = location_matches or not job_location  # Accept if no location specified in job

        return is_keyword_relevant and is_location_relevant
    
    async def crawl_all_sites(self, keyword: str, location: str = "", 
                            domain: str = "", max_pages_per_site: int = 2) -> List[Dict]:
        """Crawl jobs from all supported sites"""
        
        all_jobs = []
        
        # Combine search terms
        search_terms = [keyword]
        if location:
            search_terms.append(location)
        if domain:
            search_terms.append(domain)
        
        search_query = " ".join(search_terms)
        
        # Crawl each site
        for site_name in self.job_sites.keys():
            try:
                print(f"Crawling {site_name}...")
                site_jobs = await self.crawl_job_site(
                    site_name, search_query, location, max_pages_per_site
                )
                all_jobs.extend(site_jobs)
                print(f"Found {len(site_jobs)} jobs from {site_name}")
                
            except Exception as e:
                print(f"Failed to crawl {site_name}: {e}")
                continue
        
        # Remove duplicates based on title and company
        unique_jobs = self._remove_duplicates(all_jobs)
        
        return unique_jobs
    
    def _remove_duplicates(self, jobs: List[Dict]) -> List[Dict]:
        """Remove duplicate jobs based on title and company"""
        
        seen = set()
        unique_jobs = []
        
        for job in jobs:
            # Create a key based on title and company
            key = f"{job.get('title', '').lower()}_{job.get('company', '').lower()}"
            key = re.sub(r'\s+', '_', key)
            
            if key not in seen and key.strip('_'):
                seen.add(key)
                unique_jobs.append(job)
        
        return unique_jobs

# Global job crawler instance
job_crawler = VietnamJobCrawler()

async def search_jobs(keyword: str, location: str = "", domain: str = "", 
                     max_pages_per_site: int = 2) -> List[Dict]:
    """
    Search for jobs across Vietnamese job sites
    
    Args:
        keyword: Job title or skill keyword
        location: Location preference (e.g., "Ho Chi Minh", "Hanoi")
        domain: Industry domain (e.g., "IT", "Marketing", "Finance")
        max_pages_per_site: Maximum pages to crawl per site
    
    Returns:
        List of job dictionaries
    """
    
    if not keyword.strip():
        return []
    
    try:
        jobs = await job_crawler.crawl_all_sites(
            keyword=keyword,
            location=location,
            domain=domain,
            max_pages_per_site=max_pages_per_site
        )
        
        return jobs
        
    except Exception as e:
        print(f"Job search failed: {e}")
        return []

def get_supported_job_sites() -> List[str]:
    """Get list of supported job sites"""
    return list(job_crawler.job_sites.keys())

def format_job_for_display(job: Dict) -> Dict:
    """Format job data for display in Streamlit"""
    
    return {
        'Title': job.get('title', 'N/A'),
        'Company': job.get('company', 'N/A'),
        'Location': job.get('location', 'N/A'),
        'Salary': job.get('salary', 'Not specified'),
        'Source': job.get('source', 'N/A').title(),
        'URL': job.get('url', ''),
        'Description': job.get('description', 'No description')[:200] + '...' if len(job.get('description', '')) > 200 else job.get('description', 'No description')
    }
