"""
Database Fallback System
========================

This module provides a fallback to SQLite if MySQL is not available,
while maintaining the same interface for the support system.
"""

import os
import sqlite3
import streamlit as st
import pandas as pd
from datetime import datetime
import uuid
from typing import Dict, Optional

class DatabaseManager:
    """Database manager that can use either MySQL or SQLite"""
    
    def __init__(self):
        self.db_type = None
        self.mysql_db = None
        self.sqlite_db = None
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize database (try MySQL first, fallback to SQLite)"""
        
        # Try MySQL first
        if self._try_mysql():
            self.db_type = "mysql"
            print("✅ Using MySQL database")
        else:
            self.db_type = "sqlite"
            self._initialize_sqlite()
            print("✅ Using SQLite database (MySQL fallback)")
    
    def _try_mysql(self) -> bool:
        """Try to initialize MySQL database"""
        try:
            # Check if .env file exists
            if not os.path.exists('.env'):
                return False
            
            # Load environment variables
            env_vars = {}
            with open('.env', 'r') as f:
                for line in f:
                    if '=' in line and not line.startswith('#'):
                        key, value = line.strip().split('=', 1)
                        env_vars[key] = value
            
            # Check if MySQL variables are set
            required_vars = ['MYSQL_HOST', 'MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_DATABASE']
            if not all(var in env_vars for var in required_vars):
                return False
            
            # Try to import and initialize MySQL
            from mysql_database import mysql_db
            mysql_db.config = {
                'host': env_vars.get('MYSQL_HOST', 'localhost'),
                'port': int(env_vars.get('MYSQL_PORT', 3306)),
                'database': env_vars.get('MYSQL_DATABASE', 'streamlit_analytics'),
                'user': env_vars.get('MYSQL_USER', 'root'),
                'password': env_vars.get('MYSQL_PASSWORD', ''),
                'charset': 'utf8mb4',
                'collation': 'utf8mb4_unicode_ci'
            }
            
            # Test connection and initialize
            if mysql_db.initialize_database():
                self.mysql_db = mysql_db
                return True
            else:
                return False
                
        except Exception as e:
            print(f"MySQL initialization failed: {e}")
            return False
    
    def _initialize_sqlite(self):
        """Initialize SQLite database as fallback"""
        try:
            from admin_system import UserTracker, SupportSystem
            self.sqlite_user_tracker = UserTracker()
            self.sqlite_support_system = SupportSystem()
            return True
        except Exception as e:
            print(f"SQLite initialization failed: {e}")
            return False

class UnifiedSupportSystem:
    """Unified support system that works with both MySQL and SQLite"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def create_ticket(self, user_id: str, subject: str, description: str, priority: str = 'medium') -> str:
        """Create a new support ticket"""
        if self.db_manager.db_type == "mysql":
            from mysql_database import mysql_support_system
            return mysql_support_system.create_ticket(user_id, subject, description, priority)
        else:
            return self.db_manager.sqlite_support_system.create_ticket(
                user_id, user_id, subject, description  # SQLite version uses different parameters
            )
    
    def add_chat_message(self, ticket_id: str, sender_id: str, message: str) -> bool:
        """Add a message to chat"""
        if self.db_manager.db_type == "mysql":
            from mysql_database import mysql_support_system
            return mysql_support_system.add_chat_message(ticket_id, sender_id, message)
        else:
            return self.db_manager.sqlite_support_system.add_chat_message(
                ticket_id, 'user', sender_id, message
            )
    
    def get_user_active_tickets(self, user_id: str) -> pd.DataFrame:
        """Get user's active tickets"""
        if self.db_manager.db_type == "mysql":
            from mysql_database import mysql_support_system
            return mysql_support_system.get_user_active_tickets(user_id)
        else:
            # For SQLite, we need to adapt the data format
            tickets = self.db_manager.sqlite_support_system.get_all_tickets()
            if not tickets.empty:
                user_tickets = tickets[tickets['user_id'] == user_id]
                # Add missing columns for compatibility
                if not user_tickets.empty:
                    user_tickets['message_count'] = 0  # Placeholder
                    user_tickets['last_message_time'] = user_tickets['created_at']
                    user_tickets['status_name'] = user_tickets['status']
                    user_tickets['priority_name'] = 'medium'  # Default
                return user_tickets
            return pd.DataFrame()
    
    def get_ticket_chat(self, ticket_id: str) -> pd.DataFrame:
        """Get chat messages for a ticket"""
        if self.db_manager.db_type == "mysql":
            from mysql_database import mysql_support_system
            return mysql_support_system.get_ticket_chat(ticket_id)
        else:
            messages = self.db_manager.sqlite_support_system.get_ticket_chat(ticket_id)
            if not messages.empty:
                # Add missing columns for compatibility
                messages['username'] = messages['sender_name']
                messages['role'] = messages['sender_type'].apply(lambda x: 'admin' if x == 'admin' else 'user')
                messages['message_type'] = 'text'
                messages['is_read'] = False
                messages['sent_at'] = messages['timestamp']
                messages['message_content'] = messages['message']
            return messages
    
    def close_ticket(self, ticket_id: str, closer_id: str) -> bool:
        """Close a support ticket"""
        if self.db_manager.db_type == "mysql":
            from mysql_database import mysql_support_system
            return mysql_support_system.close_ticket(ticket_id, closer_id)
        else:
            return self.db_manager.sqlite_support_system.update_ticket_status(
                ticket_id, 'closed', closer_id
            )

# Global instances
db_manager = DatabaseManager()
unified_support_system = UnifiedSupportSystem(db_manager)

def get_db_connection():
    """Get database connection (for compatibility)"""
    if db_manager.db_type == "mysql":
        try:
            from mysql_database import get_mysql_connection
            return get_mysql_connection()
        except Exception:
            pass

    # Return SQLite connection as fallback
    try:
        return sqlite3.connect('simple_support.db')
    except Exception as e:
        print(f"Database connection failed: {e}")
        return None

def get_database_info() -> Dict[str, str]:
    """Get information about the current database"""
    return {
        'type': db_manager.db_type,
        'status': 'Connected' if db_manager.db_type else 'Not Connected',
        'description': 'MySQL with 3NF design' if db_manager.db_type == 'mysql' else 'SQLite fallback'
    }

def initialize_user_in_database(user_id: str, username: str, email: str = '', 
                               first_name: str = '', last_name: str = '', role: str = 'user'):
    """Initialize user in the appropriate database"""
    if db_manager.db_type == "mysql":
        try:
            from mysql_database import mysql_user_tracker
            return mysql_user_tracker.create_user(user_id, username, email, first_name, last_name, role)
        except Exception as e:
            print(f"Error creating MySQL user: {e}")
            return False
    else:
        # For SQLite, users are created automatically when they interact
        return True

def start_user_session(user_id: str, browser_info: Dict) -> Optional[str]:
    """Start a user session"""
    if db_manager.db_type == "mysql":
        try:
            from mysql_database import mysql_user_tracker
            return mysql_user_tracker.start_session(user_id, browser_info)
        except Exception as e:
            print(f"Error starting MySQL session: {e}")
            return None
    else:
        try:
            return db_manager.sqlite_user_tracker.start_session(
                user_id, username=user_id, browser_info=browser_info
            )
        except Exception as e:
            print(f"Error starting SQLite session: {e}")
            return None
