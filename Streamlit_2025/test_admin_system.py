#!/usr/bin/env python3
"""
Test script to verify the admin system functionality
"""

import sys
import os
import sqlite3
from datetime import datetime, timedelta

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_admin_system_imports():
    """Test that admin system modules can be imported"""
    print("🧪 Testing Admin System Imports...")
    
    try:
        from admin_system import UserTracker, SupportSystem, AdminAuth
        print("✅ Admin system core classes imported successfully")
        
        from admin_system import create_admin_dashboard
        print("✅ Admin dashboard function imported successfully")
        
        from user_support_widget import create_support_chat_widget, initialize_user_session
        print("✅ User support widget functions imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Admin system import failed: {str(e)}")
        return False

def test_database_initialization():
    """Test database initialization"""
    print("\n🧪 Testing Database Initialization...")
    
    try:
        from admin_system import UserTracker, SupportSystem
        
        # Initialize trackers
        user_tracker = UserTracker()
        support_system = SupportSystem()
        
        print("✅ UserTracker initialized successfully")
        print("✅ SupportSystem initialized successfully")
        
        # Check if database file exists
        if os.path.exists("user_analytics.db"):
            print("✅ Database file created successfully")
            
            # Check tables
            conn = sqlite3.connect("user_analytics.db")
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            expected_tables = [
                'user_sessions',
                'page_visits', 
                'user_interactions',
                'support_tickets',
                'chat_messages'
            ]
            
            table_names = [table[0] for table in tables]
            
            for expected_table in expected_tables:
                if expected_table in table_names:
                    print(f"✅ Table '{expected_table}' created successfully")
                else:
                    print(f"❌ Table '{expected_table}' missing")
                    return False
            
            conn.close()
            return True
        else:
            print("❌ Database file not created")
            return False
            
    except Exception as e:
        print(f"❌ Database initialization failed: {str(e)}")
        return False

def test_user_tracking():
    """Test user tracking functionality"""
    print("\n🧪 Testing User Tracking...")
    
    try:
        from admin_system import UserTracker
        
        user_tracker = UserTracker()
        
        # Test session creation
        browser_info = {
            'browser': 'Chrome',
            'user_agent': 'Mozilla/5.0 Test',
            'device': 'Desktop'
        }
        
        session_id = user_tracker.start_session(
            user_id="test_user",
            username="Test User",
            browser_info=browser_info
        )
        
        print(f"✅ Session created: {session_id}")
        
        # Test page visit tracking
        user_tracker.track_page_visit(session_id, "Test Page")
        print("✅ Page visit tracked")
        
        # Test interaction tracking
        user_tracker.track_interaction(
            session_id, 
            "Test Page", 
            "button_click", 
            "test_button",
            "Test interaction"
        )
        print("✅ User interaction tracked")
        
        # Test session ending
        user_tracker.end_session(session_id)
        print("✅ Session ended")
        
        # Test analytics data retrieval
        analytics = user_tracker.get_analytics_data(days=1)
        print(f"✅ Analytics data retrieved: {analytics['total_sessions']} sessions")
        
        return True
        
    except Exception as e:
        print(f"❌ User tracking test failed: {str(e)}")
        return False

def test_support_system():
    """Test support system functionality"""
    print("\n🧪 Testing Support System...")
    
    try:
        from admin_system import SupportSystem
        
        support_system = SupportSystem()
        
        # Test ticket creation
        ticket_id = support_system.create_ticket(
            user_id="test_user",
            username="Test User",
            subject="Test Support Ticket",
            message="This is a test support message"
        )
        
        print(f"✅ Support ticket created: {ticket_id}")
        
        # Test adding chat message
        support_system.add_chat_message(
            ticket_id,
            "admin",
            "admin_user",
            "This is an admin response"
        )
        print("✅ Chat message added")
        
        # Test ticket status update
        support_system.update_ticket_status(ticket_id, "in_progress", "admin_user")
        print("✅ Ticket status updated")
        
        # Test retrieving tickets
        all_tickets = support_system.get_all_tickets()
        print(f"✅ Retrieved {len(all_tickets)} tickets")
        
        # Test retrieving chat messages
        chat_messages = support_system.get_ticket_chat(ticket_id)
        print(f"✅ Retrieved {len(chat_messages)} chat messages")
        
        return True
        
    except Exception as e:
        print(f"❌ Support system test failed: {str(e)}")
        return False

def test_admin_authentication():
    """Test admin authentication"""
    print("\n🧪 Testing Admin Authentication...")
    
    try:
        from admin_system import AdminAuth
        
        # Test admin credential verification (demo credentials)
        is_valid_admin = AdminAuth.verify_admin_credentials("admin", "admin123")
        print(f"✅ Admin credential verification: {is_valid_admin}")
        
        is_invalid_admin = AdminAuth.verify_admin_credentials("admin", "wrong_password")
        print(f"✅ Invalid credential rejection: {not is_invalid_admin}")
        
        # Test admin role checking
        try:
            is_admin_role = AdminAuth.is_admin_user("admin")
            print(f"✅ Admin role check: {is_admin_role}")
        except:
            print("⚠️ Admin role check requires config.yaml - skipping")
        
        return True
        
    except Exception as e:
        print(f"❌ Admin authentication test failed: {str(e)}")
        return False

def test_data_persistence():
    """Test data persistence across sessions"""
    print("\n🧪 Testing Data Persistence...")
    
    try:
        from admin_system import UserTracker, SupportSystem
        
        # Create new instances to test persistence
        user_tracker1 = UserTracker()
        support_system1 = SupportSystem()
        
        # Get data from first instance
        analytics1 = user_tracker1.get_analytics_data(days=1)
        tickets1 = support_system1.get_all_tickets()
        
        # Create second instances
        user_tracker2 = UserTracker()
        support_system2 = SupportSystem()
        
        # Get data from second instance
        analytics2 = user_tracker2.get_analytics_data(days=1)
        tickets2 = support_system2.get_all_tickets()
        
        # Compare data
        sessions_match = analytics1['total_sessions'] == analytics2['total_sessions']
        tickets_match = len(tickets1) == len(tickets2)
        
        print(f"✅ Session data persistence: {sessions_match}")
        print(f"✅ Ticket data persistence: {tickets_match}")
        
        return sessions_match and tickets_match
        
    except Exception as e:
        print(f"❌ Data persistence test failed: {str(e)}")
        return False

def test_app_integration():
    """Test integration with main app"""
    print("\n🧪 Testing App Integration...")
    
    try:
        # Test that app can import admin functions
        import app
        
        # Check if admin functions are accessible
        if hasattr(app, 'use_admin_dashboard'):
            print("✅ Admin dashboard function available in app")
        else:
            print("❌ Admin dashboard function missing in app")
            return False
        
        # Test admin system imports in app
        from admin_system import AdminAuth, create_admin_dashboard
        from user_support_widget import create_support_chat_widget
        
        print("✅ Admin system functions importable from app context")
        
        return True
        
    except Exception as e:
        print(f"❌ App integration test failed: {str(e)}")
        return False

def cleanup_test_data():
    """Clean up test data"""
    print("\n🧹 Cleaning up test data...")
    
    try:
        if os.path.exists("user_analytics.db"):
            conn = sqlite3.connect("user_analytics.db")
            cursor = conn.cursor()
            
            # Clean up test data
            cursor.execute("DELETE FROM user_sessions WHERE user_id = 'test_user'")
            cursor.execute("DELETE FROM support_tickets WHERE user_id = 'test_user'")
            cursor.execute("DELETE FROM page_visits WHERE session_id LIKE '%test%'")
            cursor.execute("DELETE FROM user_interactions WHERE session_id LIKE '%test%'")
            cursor.execute("DELETE FROM chat_messages WHERE sender_id = 'test_user'")
            
            conn.commit()
            conn.close()
            
            print("✅ Test data cleaned up")
        
        return True
        
    except Exception as e:
        print(f"⚠️ Cleanup warning: {str(e)}")
        return True  # Don't fail tests due to cleanup issues

def main():
    """Run all admin system tests"""
    print("🚀 Testing Admin System with User Behavior Tracking...\n")
    
    # Run tests
    imports_ok = test_admin_system_imports()
    db_ok = test_database_initialization()
    tracking_ok = test_user_tracking()
    support_ok = test_support_system()
    auth_ok = test_admin_authentication()
    persistence_ok = test_data_persistence()
    integration_ok = test_app_integration()
    
    # Cleanup
    cleanup_ok = cleanup_test_data()
    
    print("\n🎉 Admin System Testing Completed!")
    
    if imports_ok and db_ok and tracking_ok and support_ok and auth_ok and persistence_ok and integration_ok:
        print("\n✅ All tests passed! The admin system is ready to use.")
        print("\n📋 Summary of Admin Features:")
        print("✅ Admin authentication with role-based access")
        print("✅ User behavior and engagement tracking")
        print("✅ Support ticket system with chat")
        print("✅ Comprehensive analytics dashboard")
        print("✅ Database persistence and data integrity")
        print("✅ Seamless app integration")
        
        print("\n🚀 Admin System Features Available:")
        print("- 📊 User Analytics Dashboard")
        print("- 👥 User Management")
        print("- 🎫 Support Ticket Management")
        print("- 💬 Live Chat Support")
        print("- ⚙️ System Settings & Export")
        print("- 📱 Floating Support Widget for Users")
        
        print("\n💡 How to Access Admin Features:")
        print("1. Log in with an admin account (role: 'admin' in config.yaml)")
        print("2. Navigate to 'Admin Dashboard' in the main menu")
        print("3. Explore analytics, manage users, and handle support")
        print("4. Users can access support chat from any page")
        
    else:
        print("\n❌ Some tests failed. Please check the issues above.")
        
        if not imports_ok:
            print("- Fix admin system import issues")
        if not db_ok:
            print("- Fix database initialization")
        if not tracking_ok:
            print("- Fix user tracking functionality")
        if not support_ok:
            print("- Fix support system functionality")
        if not auth_ok:
            print("- Fix admin authentication")
        if not persistence_ok:
            print("- Fix data persistence issues")
        if not integration_ok:
            print("- Fix app integration issues")

if __name__ == "__main__":
    main()
