# 🚀 Quick Start Guide - Job Search Feature

## ✅ **Step 1: Check Dependencies (DONE!)**

Great news! All required dependencies are now installed:
- ✅ PyPDF2 - for PDF CV processing
- ✅ python-docx - for Word document processing  
- ✅ google-generativeai - for AI job matching

## 🔑 **Step 2: Get Your FREE Google AI API Key**

### **Why do you need this?**
- The AI job matching feature uses Google's free AI to analyze your CV and match you with jobs
- It's completely **FREE** with generous usage limits
- Without it, basic job search still works, but you won't get AI-powered matching

### **How to get it (takes 2 minutes):**

1. **Go to Google AI Studio:**
   - Click: https://makersuite.google.com/app/apikey
   - Sign in with your Google account (any Gmail account works)

2. **Create API Key:**
   - Click the blue "Create API Key" button
   - Choose "Create API key in new project" (recommended)
   - **Copy the generated key** (it looks like: `AIzaSyC...`)

3. **Enter in the App:**
   - Go to Job Search → AI Matching tab
   - Paste your API key in the input field
   - Click "Test API Key" to verify it works
   - You'll see "✅ API key is valid and working!" if successful

## 🎯 **Step 3: Start Using Job Search**

### **Basic Job Search (No API key needed):**
1. Go to **Job Search** tab
2. Enter job keyword (e.g., "Python Developer", "Marketing Manager")
3. Add location (e.g., "Ho Chi Minh City", "Hanoi") 
4. Click **"Search Jobs"**
5. View results from 4 Vietnamese job sites!

### **AI-Powered Job Matching (Requires API key):**
1. Go to **AI Matching** tab
2. **Upload your CV** (PDF, Word, or text file)
3. Wait for AI to analyze your skills and experience
4. Go back to **Job Search** tab and search for jobs
5. Return to **AI Matching** tab
6. Click **"Find Best Matches"**
7. Get ranked job recommendations with match scores!

## 🔧 **Troubleshooting**

### **"Missing dependencies" warning:**
- **Fixed!** We just installed PyPDF2 and python-docx
- Restart the app if you still see the warning

### **"Google AI not available":**
- Make sure you entered the API key correctly
- Test the API key using the "Test API Key" button
- Check that you copied the complete key from Google AI Studio

### **"No jobs found":**
- Try different keywords (e.g., "developer" instead of "software engineer")
- Use broader location terms (e.g., "Ho Chi Minh" instead of specific districts)
- Some job sites may be temporarily unavailable - this is normal

### **CV upload fails:**
- Make sure file is PDF, DOCX, or TXT format
- Try a smaller file size (under 10MB)
- Check that the file isn't corrupted

## 💡 **Tips for Best Results**

### **Job Search Tips:**
- **Use Vietnamese terms:** "Lập trình viên" or "Developer"
- **Try multiple searches:** Different keywords find different jobs
- **Use location filters:** Helps find relevant positions
- **Check multiple sources:** Results come from 4 different job sites

### **AI Matching Tips:**
- **Upload detailed CV:** More information = better matching
- **Include skills section:** AI looks for technical and soft skills
- **Use standard format:** Clean, well-formatted CVs work best
- **Try different job searches:** AI matching improves with more job data

## 🎉 **You're Ready!**

Your job search system now includes:
- ✅ **Multi-site job crawling** from Vietnamese job sites
- ✅ **AI-powered CV analysis** and job matching
- ✅ **Market analytics** and insights
- ✅ **Professional interface** with export options

## 📞 **Need Help?**

1. **Use the support chat** (💬 Need Help? button) in the app
2. **Check error messages** - they usually explain what's wrong
3. **Try the basic job search first** - it works without any setup
4. **Get the API key** for full AI features

## 🚀 **Next Steps**

1. **Test basic job search** - search for any job to see it working
2. **Get your Google AI API key** - takes 2 minutes, completely free
3. **Upload your CV** - let AI analyze your skills
4. **Find your perfect job** - with AI-powered matching!

---

**🎯 Remember:** Even without the API key, you can still search jobs across multiple Vietnamese sites. The AI features are just a bonus that makes the matching even better!

**Happy job hunting!** 🚀💼
