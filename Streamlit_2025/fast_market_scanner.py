"""
Fast Market Scanner
==================

Comprehensive market scanning tool that combines fundamental analysis, 
technical analysis, and sentiment analysis to identify investment opportunities.
"""

import pandas as pd
import numpy as np
import streamlit as st
from vnstock import Vnstock
import warnings
warnings.filterwarnings('ignore')
from datetime import datetime, timedelta
import os
import re
from typing import List, Dict, Tuple, Optional
import time

# Import news crawling for sentiment analysis
try:
    from crawl_news import run_extraction
    NEWS_AVAILABLE = True
except ImportError:
    NEWS_AVAILABLE = False
    st.warning("News crawling not available for sentiment analysis")

class FastMarketScanner:
    def __init__(self):
        self.vnstock = Vnstock()
        self.blacklist = self.load_blacklist()
        
    def load_blacklist(self) -> List[str]:
        """Load blacklisted symbols from file"""
        try:
            with open('black_list.txt', 'r', encoding='utf-8') as f:
                blacklist = []
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        blacklist.append(line.upper())
                return blacklist
        except FileNotFoundError:
            st.warning("⚠️ black_list.txt not found. No symbols will be blacklisted.")
            return []
        except Exception as e:
            st.error(f"Error loading blacklist: {e}")
            return []
    
    def get_all_symbols(self) -> List[str]:
        """Get all available stock symbols excluding blacklisted ones"""
        try:
            # Get all listed companies using the correct API
            stock_obj = self.vnstock.stock()
            all_stocks = stock_obj.listing.all_symbols()

            if isinstance(all_stocks, pd.DataFrame) and 'symbol' in all_stocks.columns:
                symbols = all_stocks['symbol'].tolist()
            else:
                # Fallback: use common Vietnamese stocks
                symbols = [
                    'VIC', 'VCB', 'BID', 'CTG', 'VHM', 'HPG', 'MSN', 'VNM', 'SAB', 'GAS',
                    'PLX', 'TCB', 'MBB', 'ACB', 'TPB', 'STB', 'HDB', 'VPB', 'SHB', 'EIB',
                    'FPT', 'VRE', 'KDH', 'NVL', 'DXG', 'PDR', 'BCM', 'DIG', 'HDG', 'LDG',
                    'SSI', 'VND', 'HCM', 'VCI', 'MWG', 'FRT', 'PNJ', 'DGC', 'GMD', 'HSG',
                    'POW', 'REE', 'GEG', 'PC1', 'DPM', 'KBC', 'VGC', 'DCM', 'DRC', 'SBT'
                ]

            # Filter out blacklisted symbols
            filtered_symbols = [s for s in symbols if s.upper() not in self.blacklist]

            # Sort symbols alphabetically (vnstock returns them in reverse order)
            filtered_symbols.sort()

            # Return all symbols (no arbitrary limit) for user choice
            return filtered_symbols

        except Exception as e:
            print(f"Error getting symbols: {e}")
            return ['VIC', 'VCB', 'BID', 'CTG', 'VHM']  # Fallback symbols
    
    def get_fundamental_data(self, symbol: str) -> Dict:
        """Get fundamental analysis data for a symbol"""
        try:
            # Get financial ratios using the correct API
            stock_obj = self.vnstock.stock(symbol=symbol)
            ratios = stock_obj.finance.ratio(period='quarterly')

            if ratios.empty:
                return {}

            latest_ratios = ratios.iloc[-1] if not ratios.empty else {}

            # Extract ratio values from the multi-level column structure
            def safe_get_ratio(df, category, metric, default=0):
                try:
                    if (category, metric) in df.index:
                        return float(df[(category, metric)]) if pd.notna(df[(category, metric)]) else default
                    return default
                except:
                    return default

            return {
                'pe_ratio': safe_get_ratio(latest_ratios, 'Chỉ tiêu định giá', 'P/E'),
                'pb_ratio': safe_get_ratio(latest_ratios, 'Chỉ tiêu định giá', 'P/B'),
                'roe': safe_get_ratio(latest_ratios, 'Chỉ tiêu khả năng sinh lời', 'ROE'),
                'roa': safe_get_ratio(latest_ratios, 'Chỉ tiêu khả năng sinh lời', 'ROA'),
                'debt_ratio': safe_get_ratio(latest_ratios, 'Chỉ tiêu cơ cấu nguồn vốn', 'Debt/Equity'),
                'current_ratio': safe_get_ratio(latest_ratios, 'Chỉ tiêu thanh khoản', 'Current Ratio'),
                'gross_margin': safe_get_ratio(latest_ratios, 'Chỉ tiêu khả năng sinh lời', 'Gross Profit Margin'),
                'net_margin': safe_get_ratio(latest_ratios, 'Chỉ tiêu khả năng sinh lời', 'Net Profit Margin')
            }
        except Exception as e:
            print(f"Error getting fundamental data for {symbol}: {e}")
            return {}
    
    def get_technical_data(self, symbol: str, days: int = 30) -> Dict:
        """Get technical analysis data for a symbol"""
        try:
            # Get price data using the correct API
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            stock_obj = self.vnstock.stock(symbol=symbol)
            price_data = stock_obj.quote.history(
                start=start_date.strftime('%Y-%m-%d'),
                end=end_date.strftime('%Y-%m-%d')
            )

            if price_data.empty:
                return {}

            # Calculate technical indicators
            price_data['ma_5'] = price_data['close'].rolling(window=5).mean()
            price_data['ma_20'] = price_data['close'].rolling(window=20).mean()
            price_data['rsi'] = self.calculate_rsi(price_data['close'])
            price_data['volume_ma'] = price_data['volume'].rolling(window=10).mean()

            latest = price_data.iloc[-1]

            # Support and resistance
            support = price_data['low'].rolling(window=20).min().iloc[-1]
            resistance = price_data['high'].rolling(window=20).max().iloc[-1]

            return {
                'current_price': latest['close'],
                'ma_5': latest['ma_5'],
                'ma_20': latest['ma_20'],
                'rsi': latest['rsi'],
                'volume_ratio': latest['volume'] / latest['volume_ma'] if latest['volume_ma'] > 0 else 1,
                'support': support,
                'resistance': resistance,
                'price_change_pct': ((latest['close'] - price_data['close'].iloc[-2]) / price_data['close'].iloc[-2] * 100) if len(price_data) > 1 else 0
            }
        except Exception as e:
            print(f"Error getting technical data for {symbol}: {e}")
            return {}
    
    def calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """Calculate RSI indicator"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return pd.Series([50] * len(prices), index=prices.index)
    
    def get_sentiment_score(self, symbol: str) -> Dict:
        """Get sentiment analysis from news"""
        try:
            if not NEWS_AVAILABLE:
                return {'sentiment_score': 0, 'news_count': 0}

            # Get news data (run_extraction doesn't take parameters)
            news_data = run_extraction()

            if not news_data or len(news_data) == 0:
                return {'sentiment_score': 0, 'news_count': 0}

            # Filter news related to the symbol
            symbol_news = []
            for article in news_data:
                title = article.get('title', '').lower()
                content = article.get('content', '').lower()
                if symbol.lower() in title or symbol.lower() in content:
                    symbol_news.append(article)

            if not symbol_news:
                return {'sentiment_score': 0, 'news_count': 0}

            # Simple sentiment scoring based on keywords
            positive_keywords = ['tăng', 'tích cực', 'khả quan', 'tốt', 'lợi nhuận', 'tăng trưởng', 'mua']
            negative_keywords = ['giảm', 'tiêu cực', 'xấu', 'lỗ', 'rủi ro', 'bán', 'sụt giảm']

            sentiment_scores = []
            for article in symbol_news:
                content = (article.get('title', '') + ' ' + article.get('content', '')).lower()

                positive_count = sum(1 for word in positive_keywords if word in content)
                negative_count = sum(1 for word in negative_keywords if word in content)

                if positive_count + negative_count > 0:
                    score = (positive_count - negative_count) / (positive_count + negative_count)
                else:
                    score = 0

                sentiment_scores.append(score)

            avg_sentiment = np.mean(sentiment_scores) if sentiment_scores else 0

            return {
                'sentiment_score': avg_sentiment,
                'news_count': len(symbol_news)
            }
        except Exception as e:
            print(f"Error getting sentiment for {symbol}: {e}")
            return {'sentiment_score': 0, 'news_count': 0}
    
    def calculate_buy_price(self, current_price: float, support: float, technical_data: Dict) -> float:
        """Calculate recommended buy price"""
        try:
            # Buy price strategy: slightly above support or at technical breakout
            rsi = technical_data.get('rsi', 50)
            ma_5 = technical_data.get('ma_5', current_price)
            
            if rsi < 30:  # Oversold
                buy_price = support * 1.02  # 2% above support
            elif current_price > ma_5:  # Uptrend
                buy_price = current_price * 0.98  # 2% below current
            else:
                buy_price = (support + current_price) / 2  # Middle point
            
            return round(buy_price, 2)
        except:
            return round(current_price * 0.95, 2)
    
    def calculate_target_price(self, current_price: float, resistance: float, fundamental_data: Dict) -> float:
        """Calculate target price based on technical and fundamental analysis"""
        try:
            pe_ratio = fundamental_data.get('pe_ratio', 15)
            roe = fundamental_data.get('roe', 10)
            
            # Technical target: resistance level
            technical_target = resistance
            
            # Fundamental target: based on PE and ROE
            if pe_ratio > 0 and pe_ratio < 20 and roe > 15:
                fundamental_multiplier = 1.2  # 20% upside for good fundamentals
            elif pe_ratio > 0 and pe_ratio < 15:
                fundamental_multiplier = 1.15  # 15% upside
            else:
                fundamental_multiplier = 1.1  # 10% upside
            
            fundamental_target = current_price * fundamental_multiplier
            
            # Take the higher of technical or fundamental target
            target_price = max(technical_target, fundamental_target)
            
            return round(target_price, 2)
        except:
            return round(current_price * 1.1, 2)
    
    def generate_criteria_support(self, symbol: str, fundamental_data: Dict, technical_data: Dict, sentiment_data: Dict) -> List[str]:
        """Generate list of supporting criteria"""
        criteria = []
        
        # Fundamental criteria
        pe_ratio = fundamental_data.get('pe_ratio', 0)
        roe = fundamental_data.get('roe', 0)
        debt_ratio = fundamental_data.get('debt_ratio', 0)
        
        if 0 < pe_ratio < 15:
            criteria.append("Low PE ratio")
        if roe > 15:
            criteria.append("High ROE")
        if debt_ratio < 0.5:
            criteria.append("Low debt ratio")
        
        # Technical criteria
        rsi = technical_data.get('rsi', 50)
        current_price = technical_data.get('current_price', 0)
        ma_5 = technical_data.get('ma_5', 0)
        ma_20 = technical_data.get('ma_20', 0)
        volume_ratio = technical_data.get('volume_ratio', 1)
        
        if rsi < 30:
            criteria.append("Oversold RSI")
        elif 30 <= rsi <= 70:
            criteria.append("Neutral RSI")
        
        if current_price > ma_5 > ma_20:
            criteria.append("Uptrend (MA5 > MA20)")
        elif current_price > ma_5:
            criteria.append("Short-term uptrend")
        
        if volume_ratio > 1.5:
            criteria.append("High volume")
        elif volume_ratio > 1.2:
            criteria.append("Above average volume")
        
        # Sentiment criteria
        sentiment_score = sentiment_data.get('sentiment_score', 0)
        news_count = sentiment_data.get('news_count', 0)
        
        if sentiment_score > 0.3:
            criteria.append("Positive news sentiment")
        elif sentiment_score > 0:
            criteria.append("Slightly positive sentiment")
        
        if news_count > 0:
            criteria.append(f"{news_count} recent news articles")
        
        return criteria if criteria else ["Basic technical analysis"]
    
    def generate_strategy(self, symbol: str, current_price: float, target_price: float, technical_data: Dict, sentiment_data: Dict) -> str:
        """Generate investment strategy"""
        try:
            rsi = technical_data.get('rsi', 50)
            price_change_pct = technical_data.get('price_change_pct', 0)
            sentiment_score = sentiment_data.get('sentiment_score', 0)
            
            upside_potential = ((target_price - current_price) / current_price) * 100
            
            if upside_potential > 20:
                time_horizon = "Hold for 2-3 months"
                exit_condition = "if price reaches target or drops below 5%"
            elif upside_potential > 10:
                time_horizon = "Hold for 1-2 months"
                exit_condition = "if price reaches target or drops below 3%"
            else:
                time_horizon = "Hold for 2-4 weeks"
                exit_condition = "if no movement after 2 weeks"
            
            # Add specific conditions based on analysis
            special_conditions = []
            
            if rsi < 30:
                special_conditions.append("wait for RSI to recover above 35")
            
            if sentiment_score > 0.3:
                special_conditions.append("positive news momentum supports entry")
            elif sentiment_score < -0.3:
                special_conditions.append("wait for negative sentiment to improve")
            
            if abs(price_change_pct) > 5:
                special_conditions.append("high volatility - consider smaller position size")
            
            strategy = f"{time_horizon}, {exit_condition}"
            if special_conditions:
                strategy += f", {', '.join(special_conditions)}"
            
            return strategy
        except:
            return "Hold for 1 month, exit if no 3% gain or 5% loss"
    
    def scan_market(self, max_symbols: int = 20) -> pd.DataFrame:
        """Scan the market and return analysis results"""
        symbols = self.get_all_symbols()[:max_symbols]
        results = []
        
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        for i, symbol in enumerate(symbols):
            try:
                status_text.text(f"Analyzing {symbol}... ({i+1}/{len(symbols)})")
                progress_bar.progress((i + 1) / len(symbols))
                
                # Get all analysis data
                fundamental_data = self.get_fundamental_data(symbol)
                technical_data = self.get_technical_data(symbol)
                sentiment_data = self.get_sentiment_score(symbol)
                
                if not technical_data or 'current_price' not in technical_data:
                    continue
                
                current_price = technical_data['current_price']
                buy_price = self.calculate_buy_price(current_price, technical_data.get('support', current_price), technical_data)
                target_price = self.calculate_target_price(current_price, technical_data.get('resistance', current_price), fundamental_data)
                
                criteria = self.generate_criteria_support(symbol, fundamental_data, technical_data, sentiment_data)
                strategy = self.generate_strategy(symbol, current_price, target_price, technical_data, sentiment_data)
                
                # Get company info for segment/industry
                try:
                    stock_obj = self.vnstock.stock(symbol=symbol)
                    company_info = stock_obj.listing.symbols_by_group()
                    segment = "Unknown"
                    if isinstance(company_info, pd.DataFrame) and symbol in company_info.index:
                        segment = company_info.loc[symbol, 'industry'] if 'industry' in company_info.columns else "Unknown"
                except:
                    segment = "Unknown"
                
                results.append({
                    'symbol': symbol,
                    'segment_industry': segment,
                    'current_price': current_price,
                    'price_can_buy': buy_price,
                    'target_price': target_price,
                    'support_by': ', '.join(criteria),
                    'strategy': strategy
                })
                
                # Small delay to avoid overwhelming the API
                time.sleep(0.1)
                
            except Exception as e:
                print(f"Error analyzing {symbol}: {e}")
                continue
        
        progress_bar.empty()
        status_text.empty()
        
        return pd.DataFrame(results)

    def scan_market_with_symbols(self, symbols: List[str]) -> pd.DataFrame:
        """Scan the market with specific symbols provided by user"""
        results = []

        progress_bar = st.progress(0)
        status_text = st.empty()

        for i, symbol in enumerate(symbols):
            try:
                status_text.text(f"Analyzing {symbol}... ({i+1}/{len(symbols)})")
                progress_bar.progress((i + 1) / len(symbols))

                # Get all analysis data
                fundamental_data = self.get_fundamental_data(symbol)
                technical_data = self.get_technical_data(symbol)
                sentiment_data = self.get_sentiment_score(symbol)

                if not technical_data or 'current_price' not in technical_data:
                    continue

                current_price = technical_data['current_price']
                buy_price = self.calculate_buy_price(current_price, technical_data.get('support', current_price), technical_data)
                target_price = self.calculate_target_price(current_price, technical_data.get('resistance', current_price), fundamental_data)

                criteria = self.generate_criteria_support(symbol, fundamental_data, technical_data, sentiment_data)
                strategy = self.generate_strategy(symbol, current_price, target_price, technical_data, sentiment_data)

                # Get company info for segment/industry
                try:
                    stock_obj = self.vnstock.stock(symbol=symbol)
                    company_info = stock_obj.listing.symbols_by_group()
                    segment = "Unknown"
                    if isinstance(company_info, pd.DataFrame) and symbol in company_info.index:
                        segment = company_info.loc[symbol, 'industry'] if 'industry' in company_info.columns else "Unknown"
                except:
                    segment = "Unknown"

                results.append({
                    'symbol': symbol,
                    'segment_industry': segment,
                    'current_price': current_price,
                    'price_can_buy': buy_price,
                    'target_price': target_price,
                    'support_by': ', '.join(criteria),
                    'strategy': strategy
                })

                # Small delay to avoid overwhelming the API
                time.sleep(0.1)

            except Exception as e:
                print(f"Error analyzing {symbol}: {e}")
                continue

        progress_bar.empty()
        status_text.empty()

        return pd.DataFrame(results)

def create_fast_market_scanner():
    """Create the fast market scanner interface"""
    st.header("⚡ Fast Market Scanner")
    st.markdown("**Comprehensive market analysis combining fundamental, technical, and sentiment analysis**")

    # Configuration options
    col1, col2 = st.columns(2)

    with col1:
        include_sentiment = st.checkbox("Include sentiment analysis", value=True)

    with col2:
        auto_refresh = st.checkbox("Auto refresh (5 min)", value=False)

    # Symbol selection mode
    st.markdown("### 📋 Symbol Selection")
    selection_mode = st.radio(
        "Choose how to select symbols:",
        ["Manual Selection", "Auto Selection (First N)"],
        help="Manual: Choose specific symbols | Auto: Scan first N symbols"
    )

    selected_symbols = None
    max_symbols = None

    if selection_mode == "Manual Selection":
        # Manual symbol selection
        try:
            # Get all available symbols
            scanner_temp = FastMarketScanner()
            all_symbols = scanner_temp.get_all_symbols()

            col1, col2 = st.columns([3, 1])

            with col1:
                selected_symbols = st.multiselect(
                    f"Select symbols to analyze (choose from {len(all_symbols)} symbols):",
                    options=all_symbols,
                    default=[],
                    help="Choose specific symbols for comprehensive analysis. All 1,700+ symbols are available for selection.",
                    key="fast_scanner_symbols"
                )

            with col2:
                st.markdown("**Quick Selection:**")
                if st.button("Popular 10", key="fast_top10"):
                    # Select popular Vietnamese stocks across different sectors
                    popular_symbols = ['VIC', 'VCB', 'BID', 'CTG', 'VHM', 'HPG', 'MSN', 'VNM', 'SAB', 'GAS']
                    available_popular = [s for s in popular_symbols if s in all_symbols]
                    st.session_state.fast_scanner_symbols = available_popular
                    st.rerun()
                if st.button("First 20", key="fast_first20"):
                    st.session_state.fast_scanner_symbols = all_symbols[:20]
                    st.rerun()
                if st.button("Random 15", key="fast_random15"):
                    import random
                    random_symbols = random.sample(all_symbols, min(15, len(all_symbols)))
                    st.session_state.fast_scanner_symbols = random_symbols
                    st.rerun()
                if st.button("Clear", key="fast_clear"):
                    st.session_state.fast_scanner_symbols = []
                    st.rerun()

            if selected_symbols:
                st.success(f"✅ Selected {len(selected_symbols)} symbols for analysis")
            else:
                st.warning("⚠️ Please select at least one symbol to proceed")

        except Exception as e:
            st.error(f"Error loading symbols: {e}")
            st.info("Falling back to auto selection mode")
            selection_mode = "Auto Selection (First N)"

    if selection_mode == "Auto Selection (First N)":
        # Auto selection with slider
        max_symbols = st.slider("Max symbols to scan", 5, 50, 20)
        st.info(f"Will analyze the first {max_symbols} symbols automatically")
    
    # Validation and scan button
    can_scan = False
    if selection_mode == "Manual Selection":
        can_scan = selected_symbols and len(selected_symbols) > 0
    else:
        can_scan = max_symbols is not None

    if not can_scan:
        st.warning("⚠️ Please select symbols or configure auto selection before scanning")
        st.stop()

    # Scan button
    if st.button("🚀 Start Market Scan", type="primary"):
        with st.spinner("🔍 Scanning market... This may take a few minutes..."):
            scanner = FastMarketScanner()

            if selection_mode == "Manual Selection":
                # Use manual selection
                results_df = scanner.scan_market_with_symbols(selected_symbols)
                st.success(f"✅ Completed analysis for {len(selected_symbols)} manually selected symbols")
            else:
                # Use auto selection
                results_df = scanner.scan_market(max_symbols)
                st.success(f"✅ Completed analysis for first {max_symbols} symbols")
            
            if not results_df.empty:
                st.success(f"✅ Scan completed! Found {len(results_df)} opportunities")
                
                # Display results
                st.subheader("📊 Market Scan Results")
                
                # Format the dataframe for better display
                display_df = results_df.copy()
                display_df['current_price'] = display_df['current_price'].apply(lambda x: f"{x:,.0f}")
                display_df['price_can_buy'] = display_df['price_can_buy'].apply(lambda x: f"{x:,.0f}")
                display_df['target_price'] = display_df['target_price'].apply(lambda x: f"{x:,.0f}")
                
                st.dataframe(display_df, use_container_width=True, hide_index=True)
                
                # Summary statistics
                st.subheader("📈 Scan Summary")
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("Symbols Scanned", len(results_df))
                
                with col2:
                    avg_upside = ((results_df['target_price'] - results_df['current_price']) / results_df['current_price'] * 100).mean()
                    st.metric("Avg Upside Potential", f"{avg_upside:.1f}%")
                
                with col3:
                    high_potential = len(results_df[((results_df['target_price'] - results_df['current_price']) / results_df['current_price'] * 100) > 15])
                    st.metric("High Potential (>15%)", high_potential)
                
                with col4:
                    st.metric("Blacklisted Symbols", len(scanner.blacklist))
                
                # Download option
                csv = results_df.to_csv(index=False)
                st.download_button(
                    label="📥 Download Results as CSV",
                    data=csv,
                    file_name=f"market_scan_{datetime.now().strftime('%Y%m%d_%H%M')}.csv",
                    mime="text/csv"
                )
                
            else:
                st.warning("⚠️ No results found. Try adjusting the scan parameters.")
    
    # Auto refresh functionality
    if auto_refresh:
        st.info("🔄 Auto refresh enabled - page will refresh every 5 minutes")
        time.sleep(300)  # 5 minutes
        st.experimental_rerun()
    
    # Information section
    with st.expander("ℹ️ How to Read the Results"):
        st.markdown("""
        **Column Explanations:**
        - **Symbol:** Stock ticker symbol
        - **Segment/Industry:** Business sector classification
        - **Current Price:** Latest trading price
        - **Price Can Buy:** Recommended entry price based on technical analysis
        - **Target Price:** Projected price target based on fundamental and technical analysis
        - **Support By:** List of criteria supporting the investment thesis
        - **Strategy:** Recommended holding period and exit conditions
        
        **Analysis Components:**
        - **Fundamental Analysis:** PE ratio, ROE, debt levels, profitability margins
        - **Technical Analysis:** Moving averages, RSI, support/resistance, volume
        - **Sentiment Analysis:** News sentiment scoring based on recent articles
        
        **Risk Management:**
        - Symbols in `black_list.txt` are automatically excluded
        - Position sizing recommendations based on volatility
        - Clear exit conditions for each recommendation
        """)
