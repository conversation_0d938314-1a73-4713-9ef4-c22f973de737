from crawl4ai import As<PERSON><PERSON><PERSON><PERSON>rawler, CacheMode, CrawlerRunConfig
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
import asyncio
import json
import streamlit as st

async def fin_news_extract():
    schema_main = {
        "name": "Article",
        "baseSelector": ".cat-content article.article",
        "fields": [
            {"name": "TITLE", "selector": "h3.article-title", "type": "text"},
            {"name": "LINK", "selector": "h3.article-title a", "type": "attribute", "attribute": "href"},
            {"name": "TIME", "selector": ".article-meta > span", "type": "text"},
            {"name": "SUMMARY", "selector": ".article-desc", "type": "text"},
        ],
    }

    schema_article = {
        "name": "ArticleDetail",
        "baseSelector": "body",
        "fields": [
            {"name": "PARAGRAPHS", "selector": "p", "type": "text"},
        ],
    }

    main_extraction_strategy = JsonCssExtractionStrategy(schema_main, verbose=True)
    article_extraction_strategy = JsonCssExtractionStrategy(schema_article, verbose=True)

    async with AsyncWebCrawler() as crawler:
        config_main = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            extraction_strategy=main_extraction_strategy,
        )

        result_main = await crawler.arun(
            url="https://thoibaotaichinhvietnam.vn/",
            config=config_main,
        )

        if not result_main.extracted_content:
            return []

        articles = json.loads(result_main.extracted_content)
        article_details = []

        for article in articles:
            title = article.get("TITLE")
            link = article.get("LINK")
            time = article.get("TIME")
            summary = article.get("SUMMARY")

            if not link:
                continue
            
            from urllib.parse import urljoin
            link = urljoin("https://thoibaotaichinhvietnam.vn/", link)

            try:
                js_wait_for_content = """
                (async () => {
                    let maxRetries = 1; 
                    let retries = 0;
                    while (retries < maxRetries) {
                        let paragraphs = document.querySelectorAll('p');
                        if (paragraphs.length > 0) break;
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        retries++;
                    }
                })();
                """

                config_article = CrawlerRunConfig(
                    cache_mode=CacheMode.BYPASS,
                    extraction_strategy=article_extraction_strategy,
                    js_code=[js_wait_for_content],
                )

                result_article = await crawler.arun(url=link, config=config_article)

                if not result_article.extracted_content:
                    continue

                paragraphs = json.loads(result_article.extracted_content)
                full_content = " ".join([p["PARAGRAPHS"] for p in paragraphs if p["PARAGRAPHS"]])

                article_details.append({
                    "TITLE": title,
                    "LINK": link,
                    "TIME": time,
                    "SUMMARY": summary,
                    "CONTENT": full_content,
                })

            except Exception as e:
                print(f"[ERROR] Failed to process {link}: {e}")

        return article_details


def run_extraction():
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    return loop.run_until_complete(fin_news_extract())