import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import requests
import json
import re
from datetime import datetime, timedelta
from collections import Counter
from textblob import TextBlob
import asyncio
from typing import List, Dict, Optional

# Import existing review analysis functions
from review_analysis import (
    fetch_google_play_reviews, fetch_app_store_reviews,
    load_sentiment_model, analyze_sentiment_vietnamese, 
    extract_dominant_sentiment, process_sentiment_dataframe,
    predict_sentiment_multilingual, sentiment_to_position_3,
    sentiment_to_position_5, plot_sentiment_scale
)

class AppSearchEngine:
    """Enhanced app search engine for finding apps by name"""
    
    def __init__(self):
        self.search_cache = {}
    
    def search_google_play_apps(self, app_name: str, limit: int = 10) -> List[Dict]:
        """
        Search for apps on Google Play by name
        
        Args:
            app_name: Name of the app to search for
            limit: Maximum number of results to return
            
        Returns:
            List of app dictionaries with app info
        """
        try:
            from google_play_scraper import search
            
            # Search for apps
            results = search(
                app_name,
                lang='en',
                country='us',
                n_hits=limit
            )
            
            # Format results
            formatted_results = []
            for app in results:
                formatted_app = {
                    'title': app.get('title', 'Unknown'),
                    'appId': app.get('appId', ''),
                    'developer': app.get('developer', 'Unknown'),
                    'score': app.get('score', 0),
                    'ratings': app.get('ratings', 0),
                    'price': app.get('price', 0),
                    'free': app.get('free', True),
                    'category': app.get('genre', 'Unknown'),
                    'url': f"https://play.google.com/store/apps/details?id={app.get('appId', '')}",
                    'icon': app.get('icon', ''),
                    'summary': app.get('summary', '')
                }
                formatted_results.append(formatted_app)
            
            return formatted_results
            
        except Exception as e:
            st.error(f"Error searching Google Play: {str(e)}")
            return []
    
    def search_app_store_apps(self, app_name: str, limit: int = 10) -> List[Dict]:
        """
        Search for apps on App Store by name using iTunes Search API
        
        Args:
            app_name: Name of the app to search for
            limit: Maximum number of results to return
            
        Returns:
            List of app dictionaries with app info
        """
        try:
            # iTunes Search API
            url = "https://itunes.apple.com/search"
            params = {
                'term': app_name,
                'media': 'software',
                'entity': 'software',
                'limit': limit,
                'country': 'US'
            }
            
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            results = data.get('results', [])
            
            # Format results
            formatted_results = []
            for app in results:
                formatted_app = {
                    'title': app.get('trackName', 'Unknown'),
                    'appId': str(app.get('trackId', '')),
                    'developer': app.get('artistName', 'Unknown'),
                    'score': app.get('averageUserRating', 0),
                    'ratings': app.get('userRatingCount', 0),
                    'price': app.get('price', 0),
                    'free': app.get('price', 0) == 0,
                    'category': app.get('primaryGenreName', 'Unknown'),
                    'url': app.get('trackViewUrl', ''),
                    'icon': app.get('artworkUrl100', ''),
                    'summary': app.get('description', '')[:200] + '...' if app.get('description') else ''
                }
                formatted_results.append(formatted_app)
            
            return formatted_results
            
        except Exception as e:
            st.error(f"Error searching App Store: {str(e)}")
            return []

class ReviewAnalyticsDashboard:
    """Enhanced dashboard for review analytics with comprehensive visualizations"""
    
    def __init__(self):
        self.color_palette = {
            'positive': '#4ECDC4',
            'neutral': '#45B7D1',
            'negative': '#FF6B6B',
            'very_positive': '#2ECC71',
            'very_negative': '#E74C3C'
        }
    
    def create_sentiment_overview(self, df: pd.DataFrame) -> None:
        """Create comprehensive sentiment overview dashboard"""
        
        st.subheader("📊 Sentiment Analysis Dashboard")
        
        # Key metrics
        col1, col2, col3, col4 = st.columns(4)
        
        total_reviews = len(df)
        avg_rating = df['score'].mean() if 'score' in df.columns else 0
        
        # Sentiment distribution
        sentiment_counts = df['Dominant Sentiment'].value_counts()
        positive_pct = (sentiment_counts.get('Positive', 0) / total_reviews * 100) if total_reviews > 0 else 0
        
        with col1:
            st.metric("Total Reviews", f"{total_reviews:,}")
        with col2:
            st.metric("Average Rating", f"{avg_rating:.1f}/5" if avg_rating > 0 else "N/A")
        with col3:
            st.metric("Positive Sentiment", f"{positive_pct:.1f}%")
        with col4:
            confidence_avg = df['Confidence'].mean() if 'Confidence' in df.columns else 0
            st.metric("Avg Confidence", f"{confidence_avg:.1f}%")
    
    def create_sentiment_visualizations(self, df: pd.DataFrame) -> None:
        """Create detailed sentiment visualizations"""
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Sentiment distribution pie chart
            sentiment_counts = df['Dominant Sentiment'].value_counts()
            
            fig_pie = px.pie(
                values=sentiment_counts.values,
                names=sentiment_counts.index,
                title="Sentiment Distribution",
                color_discrete_map={
                    'Positive': self.color_palette['positive'],
                    'Neutral': self.color_palette['neutral'],
                    'Negative': self.color_palette['negative'],
                    'Very Positive': self.color_palette['very_positive'],
                    'Very Negative': self.color_palette['very_negative']
                }
            )
            st.plotly_chart(fig_pie, use_container_width=True)
        
        with col2:
            # Rating distribution if available
            if 'score' in df.columns:
                fig_rating = px.histogram(
                    df,
                    x='score',
                    title="Rating Distribution",
                    nbins=5,
                    color_discrete_sequence=[self.color_palette['positive']]
                )
                fig_rating.update_layout(
                    xaxis_title="Rating",
                    yaxis_title="Number of Reviews"
                )
                st.plotly_chart(fig_rating, use_container_width=True)
    
    def create_temporal_analysis(self, df: pd.DataFrame) -> None:
        """Create temporal analysis of reviews"""

        if 'at' in df.columns and len(df) > 0:
            st.subheader("📈 Temporal Analysis")

            try:
                # Convert timestamp to datetime
                df['review_date'] = pd.to_datetime(df['at'], errors='coerce')
                df = df.dropna(subset=['review_date'])

                if len(df) == 0:
                    st.info("No valid dates found for temporal analysis")
                    return

                df['month_year'] = df['review_date'].dt.to_period('M')

                # Monthly sentiment trends
                monthly_sentiment = df.groupby(['month_year', 'Dominant Sentiment']).size().unstack(fill_value=0)

                if len(monthly_sentiment) == 0:
                    st.info("Not enough data for temporal analysis")
                    return

                # Create a proper dataframe for plotly
                temporal_data = []
                for sentiment in monthly_sentiment.columns:
                    for month in monthly_sentiment.index:
                        temporal_data.append({
                            'Month': str(month),
                            'Sentiment': sentiment,
                            'Count': monthly_sentiment.loc[month, sentiment]
                        })

                temporal_df = pd.DataFrame(temporal_data)

                if len(temporal_df) > 0:
                    fig_temporal = px.line(
                        temporal_df,
                        x='Month',
                        y='Count',
                        color='Sentiment',
                        title="Sentiment Trends Over Time",
                        color_discrete_map={
                            'Positive': self.color_palette['positive'],
                            'Neutral': self.color_palette['neutral'],
                            'Negative': self.color_palette['negative'],
                            'Very Positive': self.color_palette.get('very_positive', '#2ECC71'),
                            'Very Negative': self.color_palette.get('very_negative', '#E74C3C')
                        }
                    )

                    fig_temporal.update_layout(
                        xaxis_title="Month",
                        yaxis_title="Number of Reviews"
                    )

                    st.plotly_chart(fig_temporal, use_container_width=True)
                else:
                    st.info("Not enough data for temporal visualization")

            except Exception as e:
                st.warning(f"Could not create temporal analysis: {str(e)}")
        else:
            st.info("No timestamp data available for temporal analysis")
    
    def create_keyword_analysis(self, df: pd.DataFrame) -> None:
        """Create keyword and theme analysis"""
        
        st.subheader("🔤 Keyword Analysis")
        
        if 'review' in df.columns:
            # Extract keywords from reviews
            all_text = ' '.join(df['review'].fillna('').astype(str))
            
            # Simple keyword extraction (can be enhanced with NLP)
            words = re.findall(r'\b[a-zA-Z]{3,}\b', all_text.lower())
            
            # Remove common stop words
            stop_words = {'the', 'and', 'app', 'this', 'that', 'with', 'for', 'are', 'was', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'has', 'let', 'put', 'say', 'she', 'too', 'use'}
            filtered_words = [word for word in words if word not in stop_words and len(word) > 3]
            
            # Get top keywords
            keyword_counts = Counter(filtered_words).most_common(20)
            
            if keyword_counts:
                keywords_df = pd.DataFrame(keyword_counts, columns=['Keyword', 'Frequency'])
                
                fig_keywords = px.bar(
                    keywords_df.head(15),
                    x='Frequency',
                    y='Keyword',
                    orientation='h',
                    title="Top Keywords in Reviews",
                    color='Frequency',
                    color_continuous_scale='viridis'
                )
                fig_keywords.update_layout(height=500)
                st.plotly_chart(fig_keywords, use_container_width=True)
    
    def create_detailed_analysis(self, df: pd.DataFrame) -> None:
        """Create detailed analysis sections"""
        
        # Review length analysis
        if 'review' in df.columns:
            st.subheader("📝 Review Analysis")
            
            col1, col2 = st.columns(2)
            
            with col1:
                # Review length distribution
                df['review_length'] = df['review'].fillna('').astype(str).str.len()
                
                fig_length = px.histogram(
                    df,
                    x='review_length',
                    title="Review Length Distribution",
                    nbins=30,
                    color_discrete_sequence=[self.color_palette['neutral']]
                )
                fig_length.update_layout(
                    xaxis_title="Review Length (characters)",
                    yaxis_title="Number of Reviews"
                )
                st.plotly_chart(fig_length, use_container_width=True)
            
            with col2:
                # Sentiment vs Review Length
                if len(df) > 0:
                    fig_scatter = px.scatter(
                        df,
                        x='review_length',
                        y='Confidence' if 'Confidence' in df.columns else 'score',
                        color='Dominant Sentiment',
                        title="Sentiment vs Review Length",
                        color_discrete_map={
                            'Positive': self.color_palette['positive'],
                            'Neutral': self.color_palette['neutral'],
                            'Negative': self.color_palette['negative']
                        }
                    )
                    st.plotly_chart(fig_scatter, use_container_width=True)
    
    def create_export_options(self, df: pd.DataFrame, app_name: str) -> None:
        """Create export options for the analysis"""
        
        st.subheader("📥 Export Analysis")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # CSV export
            csv_data = df.to_csv(index=False)
            st.download_button(
                "📄 Download CSV",
                csv_data,
                file_name=f"review_analysis_{app_name}_{datetime.now().strftime('%Y%m%d_%H%M')}.csv",
                mime="text/csv"
            )
        
        with col2:
            # JSON export
            json_data = df.to_json(orient='records', indent=2)
            st.download_button(
                "📋 Download JSON",
                json_data,
                file_name=f"review_analysis_{app_name}_{datetime.now().strftime('%Y%m%d_%H%M')}.json",
                mime="application/json"
            )
        
        with col3:
            # Summary report
            sentiment_summary = df['Dominant Sentiment'].value_counts()
            avg_rating = df['score'].mean() if 'score' in df.columns else 0
            
            summary_report = f"""
# Review Analysis Summary Report

**App:** {app_name}
**Analysis Date:** {datetime.now().strftime('%Y-%m-%d %H:%M')}
**Total Reviews Analyzed:** {len(df)}

## Sentiment Distribution:
{chr(10).join([f"- {sentiment}: {count} reviews ({count/len(df)*100:.1f}%)" for sentiment, count in sentiment_summary.items()])}

## Key Metrics:
- Average Rating: {avg_rating:.2f}/5
- Most Common Sentiment: {sentiment_summary.index[0] if len(sentiment_summary) > 0 else 'N/A'}
- Review Coverage: {len(df)} reviews analyzed

## Analysis Method:
- Sentiment analysis using advanced NLP models
- Keyword extraction and frequency analysis
- Temporal trend analysis
- Review quality assessment
            """
            
            st.download_button(
                "📊 Download Report",
                summary_report,
                file_name=f"review_summary_{app_name}_{datetime.now().strftime('%Y%m%d_%H%M')}.md",
                mime="text/markdown"
            )

def create_enhanced_review_analysis_interface():
    """Create the enhanced review analysis interface with app search and dashboard"""

    st.title("📱 Enhanced App Review Analysis")
    st.markdown("**Analyze app reviews with AI-powered insights and comprehensive dashboards**")

    # Initialize session state
    if "original_df" not in st.session_state:
        st.session_state.original_df = None
    if "analyzed_df" not in st.session_state:
        st.session_state.analyzed_df = None
    if "selected_app" not in st.session_state:
        st.session_state.selected_app = None
    if "search_results" not in st.session_state:
        st.session_state.search_results = None
    if "last_search_query" not in st.session_state:
        st.session_state.last_search_query = ""
    if "current_platform" not in st.session_state:
        st.session_state.current_platform = ""

    # Initialize components
    app_search = AppSearchEngine()
    dashboard = ReviewAnalyticsDashboard()

    # Sidebar configuration
    st.sidebar.title("🔧 Configuration")

    # App search method selection
    search_method = st.sidebar.radio(
        "How do you want to find the app?",
        ["🔍 Search by App Name", "🆔 Direct App ID/URL"],
        help="Choose whether to search for apps by name or enter app ID/URL directly"
    )

    # Platform selection
    platform = st.sidebar.radio(
        "Select Platform:",
        ["📱 Google Play", "🍎 App Store"],
        horizontal=True
    )

    # Number of reviews
    num_reviews = st.sidebar.number_input(
        "Number of reviews to fetch:",
        min_value=10,
        max_value=1000,
        value=100,
        step=10,
        help="More reviews provide better analysis but take longer to process"
    )

    # Main interface
    if search_method == "🔍 Search by App Name":
        # App name search interface
        st.subheader("🔍 Find Your App")

        col1, col2 = st.columns([3, 1])

        with col1:
            app_name = st.text_input(
                "Enter app name:",
                placeholder="e.g., Instagram, WhatsApp, TikTok",
                help="Enter the name of the app you want to analyze"
            )

        with col2:
            search_limit = st.selectbox("Results", [5, 10, 15], index=1)

        # Check if we need to perform a new search or display cached results
        search_triggered = st.button("🔍 Search Apps", type="primary") if app_name else False

        if search_triggered and app_name:
            with st.spinner(f"Searching for '{app_name}' on {platform.split()[1]}..."):
                if platform == "📱 Google Play":
                    search_results = app_search.search_google_play_apps(app_name, search_limit)
                else:
                    search_results = app_search.search_app_store_apps(app_name, search_limit)

                # Store search results in session state
                st.session_state.search_results = search_results
                st.session_state.last_search_query = app_name
                st.session_state.current_platform = platform

        # Display search results (either new or cached)
        if st.session_state.search_results and st.session_state.last_search_query:
            search_results = st.session_state.search_results

            if search_results:
                col1, col2 = st.columns([3, 1])
                with col1:
                    st.success(f"Found {len(search_results)} apps for '{st.session_state.last_search_query}'")
                with col2:
                    if st.button("🔄 New Search", help="Clear current search and start over"):
                        st.session_state.search_results = None
                        st.session_state.last_search_query = ""
                        st.rerun()

                # Display search results
                st.subheader("📱 Search Results")

                for i, app in enumerate(search_results):
                    with st.expander(f"📱 {app['title']} by {app['developer']}", expanded=(i == 0)):
                        col1, col2, col3 = st.columns([1, 2, 1])

                        with col1:
                            if app.get('icon'):
                                st.image(app['icon'], width=80)

                        with col2:
                            st.write(f"**Developer:** {app['developer']}")
                            st.write(f"**Category:** {app['category']}")
                            st.write(f"**Rating:** {app['score']:.1f}/5 ({app['ratings']:,} reviews)")
                            price_text = 'Free' if app['free'] else f"${app['price']}"
                            st.write(f"**Price:** {price_text}")
                            if app.get('summary'):
                                st.write(f"**Description:** {app['summary']}")

                        with col3:
                            if st.button(f"📊 Analyze Reviews", key=f"analyze_{i}"):
                                st.session_state.selected_app = app
                                # Don't call st.rerun() here, let the page continue
            else:
                st.warning("No apps found. Try a different search term.")

    else:
        # Direct ID/URL input interface
        st.subheader("🆔 Enter App Details")

        if platform == "📱 Google Play":
            app_input = st.text_input(
                "Google Play App URL or ID:",
                placeholder="https://play.google.com/store/apps/details?id=com.example.app",
                help="Enter the full Google Play URL or just the app ID"
            )
        else:
            app_input = st.text_input(
                "App Store App ID:",
                placeholder="123456789",
                help="Enter the numeric App Store ID (found in the app's URL)"
            )

        if app_input and st.button("📊 Analyze Reviews", type="primary", key="direct_analyze_btn"):
            # Create a mock app object for direct input
            st.session_state.selected_app = {
                'title': 'Selected App',
                'appId': app_input.split('id=')[-1] if 'id=' in app_input else app_input,
                'url': app_input if app_input.startswith('http') else '',
                'platform': platform
            }
            # Don't call st.rerun() here, let the page continue

    # Review analysis section
    if st.session_state.selected_app:
        app = st.session_state.selected_app

        st.divider()
        st.subheader(f"📊 Analyzing: {app['title']}")

        # Fetch reviews
        if st.session_state.original_df is None:
            with st.spinner(f"Fetching {num_reviews} reviews..."):
                try:
                    if platform == "📱 Google Play":
                        if app.get('url'):
                            reviews_df = fetch_google_play_reviews(app['url'], num_reviews)
                        else:
                            # Construct URL from app ID
                            url = f"https://play.google.com/store/apps/details?id={app['appId']}"
                            reviews_df = fetch_google_play_reviews(url, num_reviews)
                    else:
                        reviews_df = fetch_app_store_reviews(app['appId'], num_reviews)

                    if not reviews_df.empty:
                        st.session_state.original_df = reviews_df
                        st.success(f"✅ Successfully fetched {len(reviews_df)} reviews!")
                    else:
                        st.error("No reviews found for this app.")

                except Exception as e:
                    st.error(f"Error fetching reviews: {str(e)}")
                    st.info("Please check the app ID/URL and try again.")

        # Display original reviews
        if st.session_state.original_df is not None:
            with st.expander("📄 Original Reviews Data", expanded=False):
                st.dataframe(st.session_state.original_df, use_container_width=True)

            # Sentiment analysis configuration
            st.subheader("🧠 Sentiment Analysis Configuration")

            col1, col2 = st.columns(2)

            with col1:
                model_option = st.radio(
                    "Select Sentiment Model:",
                    ["Vietnamese", "Multilingual"],
                    help="Vietnamese model works best for Vietnamese reviews, Multilingual for other languages"
                )

            with col2:
                if st.button("🚀 Start Analysis", type="primary"):
                    with st.spinner("Analyzing sentiments..."):
                        df = st.session_state.original_df.copy()

                        if model_option == "Vietnamese":
                            model_path = "5CD-AI/Vietnamese-Sentiment-visobert"
                            tokenizer, config, model = load_sentiment_model(model_path)
                            df = process_sentiment_dataframe(df, tokenizer, config, model)
                            positions = df["Dominant Sentiment"].apply(sentiment_to_position_3)
                            segments = [(0,33,"Negative"), (33,67,"Neutral"), (67,100,"Positive")]
                            overall_score = np.mean(positions)
                            if overall_score < 33:
                                overall_label = "Negative"
                            elif overall_score < 67:
                                overall_label = "Neutral"
                            else:
                                overall_label = "Positive"
                        else:  # Multilingual
                            model_path = "tabularisai/multilingual-sentiment-analysis"
                            tokenizer, config, model = load_sentiment_model(model_path)
                            results = df["review"].apply(lambda x: predict_sentiment_multilingual(x, tokenizer, model))
                            df[["Dominant Sentiment", "Confidence"]] = results.values.tolist()
                            positions = df["Dominant Sentiment"].apply(sentiment_to_position_5)
                            overall_score = np.average(positions, weights=df["Confidence"] / 100)
                            if overall_score < 20:
                                overall_label = "Very Negative"
                            elif overall_score < 40:
                                overall_label = "Negative"
                            elif overall_score < 60:
                                overall_label = "Neutral"
                            elif overall_score < 80:
                                overall_label = "Positive"
                            else:
                                overall_label = "Very Positive"
                            segments = None

                        st.session_state.analyzed_df = df
                        st.session_state.overall_score = overall_score
                        st.session_state.overall_label = overall_label
                        st.session_state.segments = segments

                        st.success("✅ Analysis completed!")
                        # Don't call st.rerun() here, let the results display immediately

            # Display analysis results
            if st.session_state.analyzed_df is not None:
                df = st.session_state.analyzed_df

                # Overall sentiment score
                st.subheader("🎯 Overall Sentiment Score")
                col1, col2 = st.columns([2, 1])

                with col1:
                    fig = plot_sentiment_scale(
                        st.session_state.overall_score,
                        st.session_state.overall_label,
                        sentiment_score=st.session_state.overall_score,
                        segments=st.session_state.segments
                    )
                    st.plotly_chart(fig, use_container_width=True)

                with col2:
                    st.metric(
                        "Overall Score",
                        f"{st.session_state.overall_score:.1f}/100",
                        delta=f"{st.session_state.overall_label}"
                    )

                # Enhanced dashboard
                dashboard.create_sentiment_overview(df)
                dashboard.create_sentiment_visualizations(df)
                dashboard.create_temporal_analysis(df)
                dashboard.create_keyword_analysis(df)
                dashboard.create_detailed_analysis(df)

                # Analyzed data table
                with st.expander("📊 Analyzed Reviews Data", expanded=False):
                    st.dataframe(df, use_container_width=True)

                # Export options
                dashboard.create_export_options(df, app['title'])

                # Reset button
                if st.button("🔄 Reset Analysis"):
                    st.session_state.original_df = None
                    st.session_state.analyzed_df = None
                    st.session_state.selected_app = None
                    st.session_state.search_results = None
                    st.session_state.last_search_query = ""
                    st.session_state.current_platform = ""
                    st.rerun()
