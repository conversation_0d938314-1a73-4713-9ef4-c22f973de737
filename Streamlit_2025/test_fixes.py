#!/usr/bin/env python3
"""
Test script to verify that both AI Job Matching and Fast Market Scanner fixes are working
"""

def test_ai_job_matching():
    """Test AI Job Matching functionality"""
    print("Testing AI Job Matching...")
    
    try:
        from job_ai_matcher import ai_matcher
        
        # Test CV analysis
        test_cv = """
        John Doe
        Software Engineer
        
        Experience:
        - 5 years as Python Developer at Tech Corp
        - 3 years as Full Stack Developer at StartupXYZ
        
        Skills:
        - Python, JavaScript, React, Django
        - Machine Learning, Data Analysis
        - AWS, Docker, Kubernetes
        
        Education:
        - Bachelor in Computer Science
        """
        
        result = ai_matcher.analyze_cv_with_ai(test_cv)
        if isinstance(result, dict) and 'error' not in result:
            print("✅ AI CV Analysis: WORKING")
            print(f"   Extracted skills: {result.get('skills', [])}")
            return True
        else:
            print(f"❌ AI CV Analysis: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ AI Job Matching Error: {e}")
        return False

def test_fast_market_scanner():
    """Test Fast Market Scanner functionality"""
    print("\nTesting Fast Market Scanner...")
    
    try:
        from fast_market_scanner import FastMarketScanner
        scanner = FastMarketScanner()
        
        # Test symbol retrieval
        symbols = scanner.get_all_symbols()
        print(f"✅ Symbol Retrieval: WORKING ({len(symbols)} symbols)")
        
        # Test technical analysis
        tech_data = scanner.get_technical_data('VIC')
        if tech_data and 'current_price' in tech_data:
            print(f"✅ Technical Analysis: WORKING (Price: {tech_data['current_price']})")
        else:
            print("❌ Technical Analysis: Failed")
            return False
        
        # Test fundamental analysis
        fund_data = scanner.get_fundamental_data('VIC')
        print(f"✅ Fundamental Analysis: WORKING (PE: {fund_data.get('pe_ratio', 'N/A')})")
        
        # Test sentiment analysis
        sentiment_data = scanner.get_sentiment_score('VIC')
        print(f"✅ Sentiment Analysis: WORKING (Score: {sentiment_data.get('sentiment_score', 0)})")
        
        return True
        
    except Exception as e:
        print(f"❌ Fast Market Scanner Error: {e}")
        return False

def main():
    """Run all tests"""
    print("=== COMPREHENSIVE TESTING ===")
    
    ai_success = test_ai_job_matching()
    scanner_success = test_fast_market_scanner()
    
    print("\n=== TEST SUMMARY ===")
    if ai_success and scanner_success:
        print("✅ ALL TESTS PASSED!")
        print("✅ Both features are now working correctly!")
        print("✅ AI Job Matching: Fixed Part object handling")
        print("✅ Fast Market Scanner: Updated to new vnstock API")
    else:
        print("❌ Some tests failed")
        if not ai_success:
            print("❌ AI Job Matching needs more work")
        if not scanner_success:
            print("❌ Fast Market Scanner needs more work")

if __name__ == "__main__":
    main()
