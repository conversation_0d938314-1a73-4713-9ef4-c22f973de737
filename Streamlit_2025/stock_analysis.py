import asyncio
import pandas as pd
import json
import os
from datetime import datetime
from crawl4ai import AsyncWebCrawler, CrawlerRunConfig, CacheMode, BrowserConfig
from crawl4ai.extraction_strategy import JsonCssExtractionStrategy
from concurrent.futures import ThreadPoolExecutor
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
from textblob import TextBlob
import re

# Define the subdomain options for crawler
SUBDOMAINS = {
    "Newest news": "https://finance.vietstock.vn/{symbol}/tin-moi-nhat.htm",
    "Business results": "https://finance.vietstock.vn/{symbol}/ket-qua-ke-hoach-kinh-doanh.htm",
    "Dividend": "https://finance.vietstock.vn/{symbol}/co-tuc.htm",
    "Internal stakeholders": "https://finance.vietstock.vn/{symbol}/co-dong-lon-noi-bo.htm",
    "Personnel": "https://finance.vietstock.vn/{symbol}/thay-doi-nhan-su.htm",
}

def get_custom_url(symbol, subdomain_key):
    """Returns the URL with {symbol} replaced for the given subdomain key."""
    url_template = SUBDOMAINS.get(subdomain_key, SUBDOMAINS["Newest news"])
    return url_template.format(symbol=symbol)

def sanitize_for_filename(url):
    import re
    match = re.search(r'vietstock\.vn/([^/]+/[^\.]+)\.htm', url)
    if match:
        sub = match.group(1)
        parts = sub.split('/')
        if len(parts) == 2:
            first_part = parts[0]
            second_part = parts[1].replace('-', '')
            return f"{first_part}-{second_part}"
    return "default-output"

# Helper function to run async code
def run_async(coroutine):
    try:
        loop = asyncio.get_event_loop()
    except RuntimeError:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
    return loop.run_until_complete(coroutine)

async def _crawl_vietstock(symbol, subdomain_key, start_date, end_date, use_cache=True, max_pages=5):
    """
    Async function to crawl Vietstock data
    
    Args:
        symbol: Stock symbol (e.g., 'VND')
        subdomain_key: Key from SUBDOMAINS dictionary
        start_date: Start date in DD/MM/YYYY format
        end_date: End date in DD/MM/YYYY format
        use_cache: Whether to use cached data if available
        max_pages: Maximum number of pages to crawl
        
    Returns:
        List of dictionaries containing extracted data with subdomain info added
    """
    base_url = get_custom_url(symbol, subdomain_key)
    
    sanitized_subdomain = sanitize_for_filename(base_url.split('//')[1])
    date_range_str = f"{start_date.replace('/', '')}-{end_date.replace('/', '')}"
    filename = f"VS_{sanitized_subdomain}_{date_range_str}_data.json"
    
    # Check if we already have cached results
    if use_cache and os.path.exists(filename):
        with open(filename, "r", encoding="utf-8") as f:
            try:
                cached_data = json.load(f)
                # Add subdomain info to cached data
                for item in cached_data:
                    item['Subdomain'] = subdomain_key
                return cached_data
            except:
                print(f"Found cached file but couldn't read it. Crawling fresh data.")
    
    all_articles = []
    schema = {
        "name": "Article",
        "baseSelector": "div.col-sm-24.col-md-14 > div > table > tbody > tr",
        "fields": [
            {
                "name": "Title",
                "selector": "td > a.text-link.news-link",
                "type": "text"
            },
            {
                "name": "Time",
                "selector": "td.col-date",
                "type": "text"
            }
        ]
    }

    # Configure browser
    browser_config = BrowserConfig(
        headless=True,
        verbose=False
    )

    # Create a session ID for maintaining state
    session_id = f"vietstock_{symbol}_{subdomain_key}"
    
    # Define hook functions for date input
    async def on_page_context_created(page, context, **kwargs):
        return page
    
    async def after_goto(page, context, url, response, **kwargs):
        try:
            # Wait for and identify the date range filter container
            await page.wait_for_selector(".m-b.text-right", timeout=5000)
            
            # Try to find the start date input
            start_date_alternatives = [
                "#txtFromDate input[type='text']",
                "#txtFromDate .form-control",
                "input.form-control[placeholder*='From']",
                "#txtFromDate>input",
                "#txtStartDate",
                "#txtStartDateFilter"
            ]
            
            end_date_alternatives = [
                "#txtToDate input[type='text']",
                "#txtToDate .form-control",
                "input.form-control[placeholder*='To']",
                "#txtToDate>input",
                "#txtEndDate",
                "#txtEndDateFilter"
            ]
            
            # Try to find the start date input
            start_date_input = None
            for selector in start_date_alternatives:
                try:
                    start_date_input = await page.wait_for_selector(selector, timeout=1000)
                    if start_date_input:
                        start_date_selector = selector
                        break
                except:
                    continue
            
            # Try to find the end date input
            end_date_input = None
            for selector in end_date_alternatives:
                try:
                    end_date_input = await page.wait_for_selector(selector, timeout=1000)
                    if end_date_input:
                        end_date_selector = selector
                        break
                except:
                    continue
            
            if not start_date_input or not end_date_input:
                # If we still can't find the inputs, try JavaScript approach
                set_dates_js = f"""
                () => {{
                    const allInputs = document.querySelectorAll('input[type="text"]');
                    let fromInput = null;
                    let toInput = null;
                    
                    for (const input of allInputs) {{
                        const parent = input.parentElement;
                        if (input.placeholder && input.placeholder.toLowerCase().includes('from') ||
                            (parent && parent.id && parent.id.toLowerCase().includes('from')) ||
                            input.id.toLowerCase().includes('from') ||
                            input.id.toLowerCase().includes('start')) {{
                            fromInput = input;
                        }}
                        else if (input.placeholder && input.placeholder.toLowerCase().includes('to') ||
                                (parent && parent.id && parent.id.toLowerCase().includes('to')) ||
                                input.id.toLowerCase().includes('to') ||
                                input.id.toLowerCase().includes('end')) {{
                            toInput = input;
                        }}
                    }}
                    
                    if (fromInput) {{
                        fromInput.value = "{start_date}";
                        const event = new Event('change', {{ bubbles: true }});
                        fromInput.dispatchEvent(event);
                    }}
                    
                    if (toInput) {{
                        toInput.value = "{end_date}";
                        const event = new Event('change', {{ bubbles: true }});
                        toInput.dispatchEvent(event);
                    }}
                    
                    return {{ fromInputFound: !!fromInput, toInputFound: !!toInput }};
                }}
                """
                
                await page.evaluate(set_dates_js)
            else:
                # Clear and fill the date input fields if found with selectors
                await page.fill(start_date_selector, start_date)
                await page.fill(end_date_selector, end_date)
            
            # Find and click the search/apply button
            search_button_selectors = [
                "#btn-news-filter",
                "button.btn-default:has-text('Search')",
                "button.btn-default:has-text('Filter')",
                "button.btn:has-text('Apply')",
                "button[type='submit']",
                "button.btn-primary"
            ]
            
            search_button = None
            for selector in search_button_selectors:
                try:
                    search_button = await page.wait_for_selector(selector, timeout=1000)
                    if search_button:
                        break
                except:
                    continue
            
            if search_button:
                await search_button.click()
            else:
                # Try to find button by text content using JavaScript
                find_button_js = """
                () => {
                    const buttons = Array.from(document.querySelectorAll('button'));
                    const searchButton = buttons.find(button => 
                        button.textContent.toLowerCase().includes('search') || 
                        button.textContent.toLowerCase().includes('filter') || 
                        button.textContent.toLowerCase().includes('apply'));
                    
                    if (searchButton) {
                        searchButton.click();
                        return true;
                    }
                    return false;
                }
                """
                
                await page.evaluate(find_button_js)
            
            # Wait for the content to load after applying the filter
            await page.wait_for_selector("div.col-sm-24.col-md-14 > div > table > tbody > tr", timeout=10000)
            
        except Exception as e:
            print(f"Error applying date filter: {e}")
        
        return page
    
    # Create the crawler
    async with AsyncWebCrawler(config=browser_config) as crawler:
        # Set up the hooks
        crawler.crawler_strategy.set_hook("on_page_context_created", on_page_context_created)
        crawler.crawler_strategy.set_hook("after_goto", after_goto)
        
        # First page extraction
        initial_config = CrawlerRunConfig(
            cache_mode=CacheMode.BYPASS,
            verbose=False,
            wait_for='css:div.col-sm-24.col-md-14',
            extraction_strategy=JsonCssExtractionStrategy(schema, verbose=False),
            session_id=session_id
        )
        
        result = await crawler.arun(url=base_url, config=initial_config)
        
        if result.extracted_content:
            page_articles = result.extracted_content
            if isinstance(page_articles, str):
                page_articles = json.loads(page_articles)
            
            all_articles.extend(page_articles)
        
        # Handle pagination - extract page by page
        page_num = 2
        has_next_page = True
        
        while has_next_page and page_num <= max_pages:
            # Check if next button exists
            check_next_page_js = """
            const nextButton = document.querySelector("li[id='new-page-next '] > a[aria-label='next']");
            if (nextButton) {
                return true;
            } else {
                return false;
            }
            """
            
            check_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                verbose=False,
                js_code=check_next_page_js,
                js_only=True,
                session_id=session_id
            )
            
            check_result = await crawler.arun(url=base_url, config=check_config)
            
            next_page_exists = "new-page-next" in check_result.html and "aria-label=\"next\"" in check_result.html
            
            if not next_page_exists:
                has_next_page = False
                break
            
            # Click the next page button
            click_next_js = """
            const nextButton = document.querySelector("li[id='new-page-next '] > a[aria-label='next']");
            if (nextButton) {
                nextButton.scrollIntoView();
                nextButton.click();
            }
            """
            
            click_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                verbose=False,
                js_code=click_next_js,
                js_only=True,
                session_id=session_id
            )
            
            await crawler.arun(url=base_url, config=click_config)
            
            # Wait for the page to load after clicking
            wait_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                verbose=False,
                wait_for='css:div.col-sm-24.col-md-14 > div > table > tbody > tr',
                js_only=True,
                session_id=session_id
            )
            
            await crawler.arun(url=base_url, config=wait_config)
            
            # Extract data from the new page
            extract_config = CrawlerRunConfig(
                cache_mode=CacheMode.BYPASS,
                verbose=False,
                extraction_strategy=JsonCssExtractionStrategy(schema, verbose=False),
                js_only=True,
                session_id=session_id
            )
            
            page_result = await crawler.arun(url=base_url, config=extract_config)
            
            if page_result.extracted_content:
                page_articles = page_result.extracted_content
                if isinstance(page_articles, str):
                    page_articles = json.loads(page_articles)
                
                all_articles.extend(page_articles)
                page_num += 1
            else:
                has_next_page = False
        
        # Clean up the session
        try:
            await crawler.crawler_strategy.kill_session(session_id)
        except Exception:
            pass
    
    # Add subdomain information to each article
    for article in all_articles:
        article['Subdomain'] = subdomain_key
    
    # Save all collected articles for caching
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(all_articles, f, ensure_ascii=False, indent=2)
    
    return all_articles

async def crawl_all_subdomains(symbol, start_date, end_date, use_cache=True, max_pages=5):
    """
    Crawl all subdomains for a given symbol in parallel
    
    Args:
        symbol: Stock symbol (e.g., 'VND')
        start_date: Start date in DD/MM/YYYY format
        end_date: End date in DD/MM/YYYY format
        use_cache: Whether to use cached data if available
        max_pages: Maximum number of pages to crawl per subdomain
        
    Returns:
        List of dictionaries containing extracted data from all subdomains
    """
    # Create tasks for each subdomain
    tasks = []
    for subdomain_key in SUBDOMAINS.keys():
        tasks.append(_crawl_vietstock(
            symbol=symbol,
            subdomain_key=subdomain_key,
            start_date=start_date,
            end_date=end_date,
            use_cache=use_cache,
            max_pages=max_pages
        ))
    
    # Run all tasks concurrently
    results = await asyncio.gather(*tasks)
    
    # Combine results from all subdomains
    all_data = []
    for result in results:
        all_data.extend(result)
    
    return all_data

def get_all_vietstock_data(symbol, start_date=None, end_date=None, use_cache=True, max_pages=5):
    """
    Fetch data from all Vietstock subdomains for a given symbol in parallel.
    
    Args:
        symbol (str): Stock symbol (e.g., 'VND')
        start_date (str): Start date in DD/MM/YYYY format
        end_date (str): End date in DD/MM/YYYY format
        use_cache (bool): Whether to use cached data if available
        max_pages (int): Maximum number of pages to crawl per subdomain
        
    Returns:
        pandas.DataFrame: DataFrame containing the crawled data from all subdomains
    """
    # Set default dates if not provided
    if start_date is None:
        start_date = "01/01/2024"
    if end_date is None:
        end_date = datetime.now().strftime("%d/%m/%Y")
    
    # Run all crawlers in parallel
    all_data = run_async(crawl_all_subdomains(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        use_cache=use_cache,
        max_pages=max_pages
    ))
    
    # Convert to DataFrame
    if all_data:
        return pd.DataFrame(all_data)
    else:
        return pd.DataFrame(columns=["Title", "Time", "Subdomain"])

def safe_to_datetime(date_str, dayfirst=True):
    """
    Safely convert a string to datetime, trying different formats
    
    Args:
        date_str: Date string to convert
        dayfirst: Whether to interpret the first value as the day
        
    Returns:
        Datetime object or None if conversion fails
    """
    if pd.isna(date_str):
        return None
    
    try:
        # First try with the explicit dayfirst parameter
        return pd.to_datetime(date_str, dayfirst=dayfirst)
    except:
        try:
            # Try explicitly with DD/MM/YYYY format
            return pd.to_datetime(date_str, format='%d/%m/%Y')
        except:
            try:
                # Try explicitly with MM/DD/YYYY format
                return pd.to_datetime(date_str, format='%m/%d/%Y')
            except:
                try:
                    # Try with mixed format, which will attempt many formats
                    return pd.to_datetime(date_str, format='mixed')
                except:
                    # If all fails, return None
                    print(f"Could not parse date: {date_str}")
                    return None

def merge_stock_with_all_news(vnstock_df, news_df):
    """
    Merge stock price data with news data from all subdomains.
    
    Args:
        vnstock_df (pandas.DataFrame): DataFrame from Vnstock with stock prices
        news_df (pandas.DataFrame): DataFrame from Vietstock with news from all subdomains
        
    Returns:
        pandas.DataFrame: Merged DataFrame with stock prices and categorized news
    """
    if 'time' in vnstock_df.columns and 'Time' in news_df.columns:
        # Make a copy to avoid modifying the original DataFrames
        vnstock_df = vnstock_df.copy()
        news_df = news_df.copy()
        
        # Convert date formats - handle the Vietnamese/European date format (DD/MM/YYYY)
        vnstock_df['date'] = vnstock_df['time'].apply(lambda x: safe_to_datetime(x, dayfirst=True))
        news_df['date'] = news_df['Time'].apply(lambda x: safe_to_datetime(x, dayfirst=True))
        
        # Clean any rows with failed date parsing
        vnstock_df = vnstock_df.dropna(subset=['date'])
        news_df = news_df.dropna(subset=['date'])
        
        # Convert to string dates in the same format for comparison
        vnstock_df['date_str'] = vnstock_df['date'].dt.strftime('%d/%m/%Y')
        news_df['date_str'] = news_df['date'].dt.strftime('%d/%m/%Y')
        
        # Group news by date and subdomain
        news_by_date_and_type = {}
        
        for date_str, group in news_df.groupby('date_str'):
            news_by_date_and_type[date_str] = {}
            
            for subdomain, subgroup in group.groupby('Subdomain'):
                news_by_date_and_type[date_str][subdomain] = subgroup['Title'].tolist()
        
        # Add columns for each subdomain type
        for subdomain in SUBDOMAINS.keys():
            col_name = f"news_{subdomain.lower().replace(' ', '_')}"
            vnstock_df[col_name] = vnstock_df['date_str'].apply(
                lambda x: news_by_date_and_type.get(x, {}).get(subdomain, [])
            )
        
        # Also add a column with all news combined but remove duplicates
        vnstock_df['all_news'] = vnstock_df['date_str'].apply(
            lambda x: list(dict.fromkeys(
                [item for sublist in news_by_date_and_type.get(x, {}).values() for item in sublist]
            ))
        )
        
        # Clean up
        vnstock_df.drop(['date', 'date_str'], axis=1, inplace=True)

        # Keep the 'all_news' column for sentiment analysis, but drop other news columns
        news_columns_to_drop = [col for col in vnstock_df.columns if col.startswith('news_') and col != 'all_news']
        vnstock_df = vnstock_df.drop(columns=news_columns_to_drop)
    
    return vnstock_df

import pandas as pd
import numpy as np
import streamlit as st
import matplotlib.pyplot as plt
import talib
from scipy import stats, signal
from datetime import date, datetime, timedelta
from dateutil.relativedelta import relativedelta

########################################################################
################### Candlestick Pattern Function #######################
########################################################################

def candlestick_identify(df):
    no_candle_signal = True
    
    # Ensure column names match your DataFrame
    open_col, high_col, low_col, close_col = 'open', 'high', 'low', 'close'
    
    # Make sure we're working with numpy arrays for TALib
    open_prices = df[open_col].values
    high_prices = df[high_col].values
    low_prices = df[low_col].values
    close_prices = df[close_col].values
    
    #1) Morning Star(Doji, southern)
    morning_doji_star = talib.CDLMORNINGDOJISTAR(open_prices, high_prices, low_prices, close_prices)
    if len(morning_doji_star) > 0 and morning_doji_star[-1] != 0:
        st.write('Morning Doji star is found on last day.')
        no_candle_signal = False

    #2) Long Line Candle (= long white day)
    long_line_candle = talib.CDLLONGLINE(open_prices, high_prices, low_prices, close_prices)
    if len(long_line_candle) > 0 and long_line_candle[-1] > 0:
        st.write('Long line candle is found on last day.')
        no_candle_signal = False

    #3) Bullish marubozu (= Marubozu, closing white)
    closing_marubozu = talib.CDLCLOSINGMARUBOZU(open_prices, high_prices, low_prices, close_prices)
    if len(closing_marubozu) > 0 and closing_marubozu[-1] > 0:
        st.write('Bullish marubozu is found on last day.')
        no_candle_signal = False

    #4) Bullish marubozu (= Marubozu, white)
    marubozu = talib.CDLMARUBOZU(open_prices, high_prices, low_prices, close_prices)
    if len(marubozu) > 0 and marubozu[-1] > 0:
        st.write('Bullish marubozu is found on last day.')
        no_candle_signal = False
        
    #5) Engulfing pattern (adding from your code)
    engulfing = talib.CDLENGULFING(open_prices, high_prices, low_prices, close_prices)
    if len(engulfing) > 0 and engulfing[-1] > 0:
        st.write('Bullish Engulfing pattern found on last day.')
        no_candle_signal = False
    elif len(engulfing) > 0 and engulfing[-1] < 0:
        st.write('Bearish Engulfing pattern found on last day.')

    if no_candle_signal:
        st.markdown(f'<h3 style="color:#b3aaaa;font-size:24px;">{"Neutral"}</h3>', unsafe_allow_html=True)
        st.write('No bullish candlestick pattern identified.')
    else:
        st.markdown(f'<h3 style="color:#33ff33;font-size:24px;">{"Bullish"}</h3>', unsafe_allow_html=True)

########################################################################
################## Functions for Consolidation/Breakout Check ##########
########################################################################

def is_consolidating(df, percentage=2):
    # Check if we have enough data
    if len(df) < 15:
        return False
        
    # Look at the past 15 days (3 weeks)
    recent_data = df.iloc[-15:]
    max_price = recent_data['close'].max()
    min_price = recent_data['close'].min()
    
    # Calculate the threshold percentage
    threshold = max_price * (1 - percentage/100)
    
    consolidating = min_price >= threshold
    return consolidating

def is_breaking_out(df):
    # Check if we have enough data
    if len(df) < 16:
        return False
        
    # Check the past 15 days (excluding today)
    if len(df) >= 16:
        past_data = df.iloc[-16:-1]
        max_price = past_data['close'].max()
        current_price = df['close'].iloc[-1]
        
        breaking_out = current_price > max_price
        return breaking_out
    return False

def consolidation_breakout_check(df):
    # First check if we have enough data
    if len(df) < 16:
        st.markdown(f'<h3 style="color:#b3aaaa;font-size:24px;">{"Insufficient Data"}</h3>', unsafe_allow_html=True)
        st.write('Not enough data for consolidation/breakout analysis (need at least 16 days).')
        return "Insufficient Data"
    
    # Determine consolidation and breakout status
    consolidating = is_consolidating(df)
    breaking_out = is_breaking_out(df)
    
    # Set result and display appropriate message with matching format to other checks
    if breaking_out:
        st.markdown(f'<h3 style="color:#33ff33;font-size:24px;">{"Breakout"}</h3>', unsafe_allow_html=True)
        st.write('Stock is breaking out (price above the high of past 15 days).')
        return "Breakout"
    elif consolidating:
        st.markdown(f'<h3 style="color:#ffcc00;font-size:24px;">{"Consolidating"}</h3>', unsafe_allow_html=True)
        st.write('Stock is consolidating (price movement within 2% range over past 15 days).')
        return "Consolidating"
    else:
        st.markdown(f'<h3 style="color:#b3aaaa;font-size:24px;">{"Neutral"}</h3>', unsafe_allow_html=True)
        st.write('No consolidation or breakout pattern detected.')
        return "Neutral"

########################################################################
#################### Trend Template Functions ##########################
########################################################################

def find_slope(data):
    """Calculate slope of data series using linear regression"""
    if len(data) < 2:
        return 0
    slope, intercept, r, p, se = stats.linregress(range(len(data)), data)
    return slope

def trend_template_filter(df):
    # Ensure we have enough data
    if len(df) < 252:  # Need at least a year of data
        st.warning("Not enough historical data for complete trend template analysis")
        criteria_met = 0
        criteria = {}
        return criteria_met, criteria
        
    # Ensure column names are lowercase to match your data
    close_col = 'close'
    
    # Calculate required moving averages
    df['50_MA'] = df[close_col].rolling(window=50).mean()
    df['150_MA'] = df[close_col].rolling(window=150).mean()
    df['200_MA'] = df[close_col].rolling(window=200).mean()

    # Find 52 week high and low
    df['52W_Low'] = df[close_col].rolling(window=252).min()
    df['52W_High'] = df[close_col].rolling(window=252).max()

    # Check each criterion (safely accessing the last row)
    current_price = df[close_col].iloc[-1] if not df.empty else 0
    
    # Safe access to calculated MAs
    ma_50 = df['50_MA'].iloc[-1] if '50_MA' in df.columns and not df['50_MA'].isna().all() else 0
    ma_150 = df['150_MA'].iloc[-1] if '150_MA' in df.columns and not df['150_MA'].isna().all() else 0
    ma_200 = df['200_MA'].iloc[-1] if '200_MA' in df.columns and not df['200_MA'].isna().all() else 0
    
    # Calculate slope for 200-day MA (past month), with safeguards
    ma_200_recent = df['200_MA'].dropna().iloc[-20:] if '200_MA' in df.columns else pd.Series()
    slope_200ma = find_slope(ma_200_recent.values) if len(ma_200_recent) >= 2 else 0
    
    # Get 52-week values with safeguards
    low_52w = df['52W_Low'].iloc[-1] if '52W_Low' in df.columns and not df['52W_Low'].isna().all() else 0
    high_52w = df['52W_High'].iloc[-1] if '52W_High' in df.columns and not df['52W_High'].isna().all() else 0
    
    # Define criteria
    criteria = {
        "Is the current stock price above both the 150-day and 200-day moving average?": 
            current_price > ma_150 and current_price > ma_200,
            
        "Is the 150-day moving average above the 200-day moving average?": 
            ma_150 > ma_200,
            
        "Has the 200-day moving average trending up for at least 1 month?": 
            slope_200ma > 0,
            
        "Is the 50-day moving average above both the 150-day and 200-day moving average?": 
            ma_50 > ma_150 and ma_50 > ma_200,
            
        "Is the current stock price above the 50-day moving average?": 
            current_price > ma_50,
            
        "Is the current stock price at least 30% above its 52-week low?": 
            current_price > (low_52w * 1.3) if low_52w > 0 else False,
            
        "Is the current stock price within at least 25% of its 52-week high?": 
            current_price > (high_52w * 0.75) if high_52w > 0 else False
    }
    
    # Count criteria met
    criteria_met = sum(criteria.values())
    
    st.subheader(f"Mark Minervini's Trend Template fulfillment: {criteria_met}/7")
    
    # Display results in columns
    cols = st.columns(3)
    criterion_items = list(criteria.items())
    
    for i, col in enumerate(cols):
        with col:
            for j in range(i, len(criterion_items), 3):
                if j < len(criterion_items):
                    question, is_met = criterion_items[j]
                    st.write(f"{question}")
                    st.write("Yes ✅" if is_met else "No ❌")
    
    return criteria_met, criteria

########################################################################
######################### VCP Functions ################################
########################################################################

def VCP_detection(df):
    """Detect VCP pattern based on volatility contraction"""
    # Check if we have enough data
    if len(df) < 40:  # Need at least 40 days for calculating volatility
        st.warning("Not enough data for VCP detection")
        return "Insufficient Data"
        
    # Calculate rolling volatility (20-day window)
    df['volatility'] = df['close'].pct_change().rolling(20).std()
    
    # Check for decreasing volatility (simple implementation)
    # Safely access last value
    if 'volatility' in df.columns and not df['volatility'].isna().all():
        last_volatility = df['volatility'].iloc[-1]
        
        # Get previous volatility if available (20 days ago)
        prev_index = -21 if len(df) > 21 else 0
        prev_volatility = df['volatility'].iloc[prev_index] if prev_index < 0 else None
        
        if prev_volatility is not None and not np.isnan(prev_volatility) and not np.isnan(last_volatility):
            if last_volatility < prev_volatility * 0.75:
                st.markdown(f'<h3 style="color:#33ff33;font-size:24px;">{"Bullish"}</h3>', unsafe_allow_html=True)
                st.write('VCP pattern detected (volatility contracting).')
                return "VCP Detected"
    
    # Default response
    st.markdown(f'<h3 style="color:#ff0d00;font-size:24px;">{"Bearish"}</h3>', unsafe_allow_html=True)
    st.write('No VCP pattern detected.')
    return "No VCP Pattern"

########################################################################
######################### News Sentiment Analysis #############################
########################################################################

def analyze_news_sentiment(news_list: list) -> dict:
    """
    Analyze sentiment of news articles using TextBlob

    Args:
        news_list: List of news titles/content

    Returns:
        Dictionary with sentiment analysis results
    """
    if not news_list or len(news_list) == 0:
        return {
            'sentiment_score': 0,
            'sentiment_label': 'Neutral',
            'positive_count': 0,
            'negative_count': 0,
            'neutral_count': 0,
            'total_count': 0
        }

    sentiments = []
    positive_count = 0
    negative_count = 0
    neutral_count = 0

    for news in news_list:
        if isinstance(news, str) and news.strip():
            # Clean the text
            cleaned_text = re.sub(r'[^\w\s]', '', news)

            # Analyze sentiment
            blob = TextBlob(cleaned_text)
            polarity = blob.sentiment.polarity

            sentiments.append(polarity)

            if polarity > 0.1:
                positive_count += 1
            elif polarity < -0.1:
                negative_count += 1
            else:
                neutral_count += 1

    if not sentiments:
        return {
            'sentiment_score': 0,
            'sentiment_label': 'Neutral',
            'positive_count': 0,
            'negative_count': 0,
            'neutral_count': 0,
            'total_count': 0
        }

    avg_sentiment = np.mean(sentiments)

    # Determine overall sentiment label
    if avg_sentiment > 0.1:
        sentiment_label = 'Positive'
    elif avg_sentiment < -0.1:
        sentiment_label = 'Negative'
    else:
        sentiment_label = 'Neutral'

    return {
        'sentiment_score': avg_sentiment,
        'sentiment_label': sentiment_label,
        'positive_count': positive_count,
        'negative_count': negative_count,
        'neutral_count': neutral_count,
        'total_count': len(sentiments)
    }

def create_news_sentiment_visualization(sentiment_data: dict, symbol: str) -> None:
    """
    Create visualization for news sentiment analysis

    Args:
        sentiment_data: Dictionary with sentiment analysis results
        symbol: Stock symbol
    """
    st.subheader(f"📰 News Sentiment Analysis for {symbol}")

    if sentiment_data['total_count'] == 0:
        st.warning("No news data available for sentiment analysis")
        return

    # Display metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Overall Sentiment", sentiment_data['sentiment_label'])

    with col2:
        st.metric("Sentiment Score", f"{sentiment_data['sentiment_score']:.3f}")

    with col3:
        st.metric("Total News", sentiment_data['total_count'])

    with col4:
        positive_ratio = sentiment_data['positive_count'] / sentiment_data['total_count'] * 100
        st.metric("Positive %", f"{positive_ratio:.1f}%")

    # Create pie chart for sentiment distribution
    sentiment_counts = [
        sentiment_data['positive_count'],
        sentiment_data['neutral_count'],
        sentiment_data['negative_count']
    ]

    labels = ['Positive', 'Neutral', 'Negative']
    colors = ['#4ECDC4', '#45B7D1', '#FF6B6B']

    fig_pie = go.Figure(data=[go.Pie(
        labels=labels,
        values=sentiment_counts,
        marker_colors=colors,
        hole=0.3
    )])

    fig_pie.update_layout(
        title="News Sentiment Distribution",
        height=400
    )

    st.plotly_chart(fig_pie, use_container_width=True)

    # Sentiment gauge
    fig_gauge = go.Figure(go.Indicator(
        mode = "gauge+number",
        value = sentiment_data['sentiment_score'],
        domain = {'x': [0, 1], 'y': [0, 1]},
        title = {'text': "Sentiment Score"},
        gauge = {
            'axis': {'range': [-1, 1]},
            'bar': {'color': "darkblue"},
            'steps': [
                {'range': [-1, -0.1], 'color': "lightcoral"},
                {'range': [-0.1, 0.1], 'color': "lightgray"},
                {'range': [0.1, 1], 'color': "lightgreen"}
            ],
            'threshold': {
                'line': {'color': "red", 'width': 4},
                'thickness': 0.75,
                'value': 0
            }
        }
    ))

    fig_gauge.update_layout(height=300)
    st.plotly_chart(fig_gauge, use_container_width=True)

########################################################################
######################### Enhanced Visualizations #############################
########################################################################

def create_enhanced_price_chart(df: pd.DataFrame, symbol: str, news_sentiment: dict = None) -> None:
    """
    Create enhanced price chart with technical indicators and sentiment overlay

    Args:
        df: DataFrame with OHLCV data
        symbol: Stock symbol
        news_sentiment: Optional sentiment data to overlay
    """
    st.subheader(f"📈 Enhanced Price Analysis for {symbol}")

    if df.empty:
        st.warning("No price data available")
        return

    # Create subplots
    fig = make_subplots(
        rows=3, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.05,
        subplot_titles=('Price & Moving Averages', 'Volume', 'Technical Indicators'),
        row_heights=[0.6, 0.2, 0.2]
    )

    # Calculate technical indicators
    df['ma_20'] = df['close'].rolling(window=20).mean()
    df['ma_50'] = df['close'].rolling(window=50).mean()
    df['bb_upper'] = df['ma_20'] + (df['close'].rolling(window=20).std() * 2)
    df['bb_lower'] = df['ma_20'] - (df['close'].rolling(window=20).std() * 2)

    # RSI calculation
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))

    # Price chart with candlesticks
    fig.add_trace(
        go.Candlestick(
            x=df['time'],
            open=df['open'],
            high=df['high'],
            low=df['low'],
            close=df['close'],
            name='Price'
        ),
        row=1, col=1
    )

    # Moving averages
    fig.add_trace(
        go.Scatter(x=df['time'], y=df['ma_20'], name='MA 20', line=dict(color='orange')),
        row=1, col=1
    )

    fig.add_trace(
        go.Scatter(x=df['time'], y=df['ma_50'], name='MA 50', line=dict(color='blue')),
        row=1, col=1
    )

    # Bollinger Bands
    fig.add_trace(
        go.Scatter(x=df['time'], y=df['bb_upper'], name='BB Upper', line=dict(color='gray', dash='dash')),
        row=1, col=1
    )

    fig.add_trace(
        go.Scatter(x=df['time'], y=df['bb_lower'], name='BB Lower', line=dict(color='gray', dash='dash')),
        row=1, col=1
    )

    # Volume
    fig.add_trace(
        go.Bar(x=df['time'], y=df['volume'], name='Volume', marker_color='lightblue'),
        row=2, col=1
    )

    # RSI
    fig.add_trace(
        go.Scatter(x=df['time'], y=df['rsi'], name='RSI', line=dict(color='purple')),
        row=3, col=1
    )

    # RSI overbought/oversold lines
    fig.add_hline(y=70, line_dash="dash", line_color="red", row=3, col=1)
    fig.add_hline(y=30, line_dash="dash", line_color="green", row=3, col=1)

    # Update layout
    fig.update_layout(
        title=f"Technical Analysis Dashboard - {symbol}",
        height=800,
        xaxis_rangeslider_visible=False
    )

    st.plotly_chart(fig, use_container_width=True)

    # Add sentiment overlay if available
    if news_sentiment and news_sentiment['total_count'] > 0:
        st.info(f"📰 Current News Sentiment: {news_sentiment['sentiment_label']} "
                f"(Score: {news_sentiment['sentiment_score']:.3f})")

########################################################################
######################### Vietnamese Market Analysis #############################
########################################################################

def vietnamese_market_analysis(symbol):
    """
    Alternative analysis function for Vietnamese market focusing on:
    1. Trading volume trends
    2. Foreign investor flow
    3. Price action and key support/resistance levels
    
    Args:
        symbol (str): Stock symbol for Vietnamese market
    
    Returns:
        dict: Analysis results
    """
    try:
        import pandas as pd
        import numpy as np
        from vnstock import Vnstock
        stock = Vnstock().stock(symbol='VND', source='VCI')
        
        # Check if symbol exists
        if not symbol:
            st.markdown(f'<h3 style="color:#b3aaaa;font-size:24px;">{"No Symbol Provided"}</h3>', unsafe_allow_html=True)
            st.write("Please provide a valid stock symbol for analysis.")
            return None
        
        # Define time periods for analysis
        end_date = pd.Timestamp.now().strftime('%Y-%m-%d')
        start_date_short = (pd.Timestamp.now() - pd.Timedelta(days=30)).strftime('%Y-%m-%d')
        start_date_long = (pd.Timestamp.now() - pd.Timedelta(days=180)).strftime('%Y-%m-%d')
        
        # Get historical price and volume data
        try:
            historical_data = stock.quote.history(symbol=symbol, start=start_date_long, end=end_date)
            
            if historical_data.empty:
                st.markdown(f'<h3 style="color:#b3aaaa;font-size:24px;">{"No Data Available"}</h3>', unsafe_allow_html=True)
                st.write(f"No historical data available for {symbol}.")
                return None
                
            # Make sure we have all required columns
            required_columns = ['time', 'close', 'volume']
            if not all(col in historical_data.columns for col in required_columns):
                st.markdown(f'<h3 style="color:#b3aaaa;font-size:24px;">{"Missing Data"}</h3>', unsafe_allow_html=True)
                st.write(f"Historical data missing required columns.")
                return None
                
            # 1. TRADING VOLUME ANALYSIS
            st.markdown(f'<h3 style="color:#b3aaaa;font-size:24px;">{"Trading Volume Analysis"}</h3>', unsafe_allow_html=True)
            
            # Calculate moving averages for volume
            historical_data['volume_ma10'] = historical_data['volume'].rolling(window=10).mean()
            historical_data['volume_ma30'] = historical_data['volume'].rolling(window=30).mean()
            
            # Get recent data
            recent_data = historical_data.iloc[-10:].copy()
            
            # Volume trend analysis
            recent_vol = recent_data['volume'].mean()
            vol_ma10 = recent_data['volume_ma10'].iloc[-1]
            vol_ma30 = recent_data['volume_ma30'].iloc[-1]
            
            # Determine volume trend
            if recent_vol > vol_ma30 * 1.5:
                st.markdown(f'<h4 style="color:#33ff33;font-size:18px;">{"Unusually High Volume"}</h4>', unsafe_allow_html=True)
                st.write(f"Recent volume is significantly above 30-day average. This often indicates strong interest.")
            elif recent_vol > vol_ma10 * 1.2:
                st.markdown(f'<h4 style="color:#33ff33;font-size:18px;">{"Elevated Volume"}</h4>', unsafe_allow_html=True)
                st.write(f"Recent volume is above 10-day average. Watch for continuation of this trend.")
            elif recent_vol < vol_ma30 * 0.5:
                st.markdown(f'<h4 style="color:#ff0d00;font-size:18px;">{"Very Low Volume"}</h4>', unsafe_allow_html=True)
                st.write(f"Recent volume is significantly below average. Indicates lack of interest.")
            elif recent_vol < vol_ma10 * 0.8:
                st.markdown(f'<h4 style="color:#ff0d00;font-size:18px;">{"Declining Volume"}</h4>', unsafe_allow_html=True)
                st.write(f"Recent volume is below 10-day average. May indicate waning interest.")
            else:
                st.markdown(f'<h4 style="color:#b3aaaa;font-size:18px;">{"Normal Volume"}</h4>', unsafe_allow_html=True)
                st.write(f"Recent volume is within normal range compared to historical averages.")
            
            # Display recent volume data
            st.write(f"Recent average daily volume: {int(recent_vol):,}")
            st.write(f"10-day average volume: {int(vol_ma10):,}")
            st.write(f"30-day average volume: {int(vol_ma30):,}")
            
            # 2. PRICE ACTION AND SUPPORT/RESISTANCE ANALYSIS
            st.markdown(f'<h3 style="color:#b3aaaa;font-size:24px;">{"Price Action & Support/Resistance"}</h3>', unsafe_allow_html=True)
            
            # Calculate key technical indicators
            historical_data['SMA20'] = historical_data['close'].rolling(window=20).mean()
            historical_data['SMA50'] = historical_data['close'].rolling(window=50).mean()
            historical_data['SMA100'] = historical_data['close'].rolling(window=100).mean()
            
            # Identify local highs and lows for support/resistance
            # This is a simplified implementation - more sophisticated methods exist
            window = 10
            historical_data['rolling_high'] = historical_data['high'].rolling(window=window, center=True).max()
            historical_data['rolling_low'] = historical_data['low'].rolling(window=window, center=True).min()
            
            # Get current price
            current_price = historical_data['close'].iloc[-1]
            
            # Get SMA values
            sma20 = historical_data['SMA20'].iloc[-1]
            sma50 = historical_data['SMA50'].iloc[-1]
            sma100 = historical_data['SMA100'].iloc[-1]
            
            # Find potential support/resistance levels
            recent_data = historical_data.iloc[-60:].copy()  # Last ~3 months
            
            # Identify resistance levels above current price
            resistance_levels = []
            for i in range(1, len(recent_data) - window):
                if recent_data['rolling_high'].iloc[i] > current_price:
                    # Check if this is a local peak
                    if (recent_data['rolling_high'].iloc[i] > recent_data['rolling_high'].iloc[i-1] and 
                        recent_data['rolling_high'].iloc[i] > recent_data['rolling_high'].iloc[i+1]):
                        resistance_levels.append(recent_data['rolling_high'].iloc[i])
            
            # Identify support levels below current price
            support_levels = []
            for i in range(1, len(recent_data) - window):
                if recent_data['rolling_low'].iloc[i] < current_price:
                    # Check if this is a local bottom
                    if (recent_data['rolling_low'].iloc[i] < recent_data['rolling_low'].iloc[i-1] and 
                        recent_data['rolling_low'].iloc[i] < recent_data['rolling_low'].iloc[i+1]):
                        support_levels.append(recent_data['rolling_low'].iloc[i])
            
            # Get closest levels
            resistance_levels = sorted(set([round(level, 2) for level in resistance_levels]))[:3]
            support_levels = sorted(set([round(level, 2) for level in support_levels]), reverse=True)[:3]
            
            # Determine trend based on SMAs
            if current_price > sma20 > sma50 > sma100:
                st.markdown(f'<h4 style="color:#33ff33;font-size:18px;">{"Strong Uptrend"}</h4>', unsafe_allow_html=True)
            elif current_price > sma20 and current_price > sma50:
                st.markdown(f'<h4 style="color:#33ff33;font-size:18px;">{"Uptrend"}</h4>', unsafe_allow_html=True)
            elif current_price < sma20 < sma50 < sma100:
                st.markdown(f'<h4 style="color:#ff0d00;font-size:18px;">{"Strong Downtrend"}</h4>', unsafe_allow_html=True)
            elif current_price < sma20 and current_price < sma50:
                st.markdown(f'<h4 style="color:#ff0d00;font-size:18px;">{"Downtrend"}</h4>', unsafe_allow_html=True)
            else:
                st.markdown(f'<h4 style="color:#b3aaaa;font-size:18px;">{"Sideways/Consolidating"}</h4>', unsafe_allow_html=True)
            
            # Display current price relative to moving averages
            st.write(f"Current price: {current_price:,.2f}")
            st.write(f"20-day SMA: {sma20:,.2f} ({'Above' if current_price > sma20 else 'Below'})")
            st.write(f"50-day SMA: {sma50:,.2f} ({'Above' if current_price > sma50 else 'Below'})")
            st.write(f"100-day SMA: {sma100:,.2f} ({'Above' if current_price > sma100 else 'Below'})")
            
            # Display support and resistance levels
            if resistance_levels:
                st.write("Key resistance levels:")
                for level in resistance_levels:
                    st.write(f"- {level:,.2f} ({((level/current_price)-1)*100:,.2f}% from current)")
            
            if support_levels:
                st.write("Key support levels:")
                for level in support_levels:
                    st.write(f"- {level:,.2f} ({((level/current_price)-1)*100:,.2f}% from current)")
            
            # Overall market sentiment based on combined factors
            sentiment_score = 0
            
            # Volume factors
            if recent_vol > vol_ma30 * 1.3:
                sentiment_score += 1
            elif recent_vol < vol_ma30 * 0.7:
                sentiment_score -= 1
                
            # Price trend factors
            if current_price > sma20 > sma50:
                sentiment_score += 1
            elif current_price < sma20 < sma50:
                sentiment_score -= 1
                
            # Add foreign flow sentiment if available
            try:
                if 'net_flow' in locals() and net_flow > 0:
                    sentiment_score += 1
                elif 'net_flow' in locals() and net_flow < 0:
                    sentiment_score -= 1
            except:
                pass
                
            # Overall market sentiment
            st.markdown(f'<h3 style="color:#b3aaaa;font-size:24px;">{"Overall Market Sentiment"}</h3>', unsafe_allow_html=True)
            
            if sentiment_score >= 2:
                st.markdown(f'<h4 style="color:#33ff33;font-size:18px;">{"Bullish"}</h4>', unsafe_allow_html=True)
                st.write("Multiple indicators suggest bullish sentiment.")
            elif sentiment_score <= -2:
                st.markdown(f'<h4 style="color:#ff0d00;font-size:18px;">{"Bearish"}</h4>', unsafe_allow_html=True)
                st.write("Multiple indicators suggest bearish sentiment.")
            elif sentiment_score == 1:
                st.markdown(f'<h4 style="color:#33ff33;font-size:18px;">{"Slightly Bullish"}</h4>', unsafe_allow_html=True)
                st.write("Some indicators suggest mild bullish sentiment.")
            elif sentiment_score == -1:
                st.markdown(f'<h4 style="color:#ff0d00;font-size:18px;">{"Slightly Bearish"}</h4>', unsafe_allow_html=True)
                st.write("Some indicators suggest mild bearish sentiment.")
            else:
                st.markdown(f'<h4 style="color:#b3aaaa;font-size:18px;">{"Neutral"}</h4>', unsafe_allow_html=True)
                st.write("Market indicators are mixed or neutral.")
                
            return {
                "symbol": symbol,
                "sentiment_score": sentiment_score,
                "volume_analysis": {
                    "recent_vol": recent_vol,
                    "vol_ma10": vol_ma10,
                    "vol_ma30": vol_ma30
                },
                "price_action": {
                    "current_price": current_price,
                    "sma20": sma20,
                    "sma50": sma50,
                    "sma100": sma100,
                    "resistance_levels": resistance_levels,
                    "support_levels": support_levels
                }
            }
            
        except Exception as e:
            st.markdown(f'<h3 style="color:#b3aaaa;font-size:24px;">{"Historical Data Error"}</h3>', unsafe_allow_html=True)
            st.write(f"Error retrieving historical data: {str(e)}")
            return None
            
    except Exception as e:
        st.markdown(f'<h3 style="color:#b3aaaa;font-size:24px;">{"Analysis Error"}</h3>', unsafe_allow_html=True)
        st.write(f"Error in analysis: {str(e)}")
        return None

########################################################################
######################### Main Analysis Function #######################
########################################################################

def analyze_stock(df, symbol=None, news_data=None):
    """Main function to run all analyses with enhanced features"""
    st.header("📊 Enhanced Technical Analysis Dashboard")

    # Ensure DataFrame has expected columns and is not empty
    required_cols = ['open', 'high', 'low', 'close']
    if df is None or df.empty:
        st.error("No data available for analysis")
        return

    if not all(col in df.columns for col in required_cols):
        st.error("DataFrame missing required columns. Ensure 'open', 'high', 'low', 'close' are present.")
        return

    # --- Enhanced Price Chart ---
    news_sentiment = None
    if news_data is not None and 'all_news' in news_data.columns:
        # Analyze news sentiment
        all_news = []
        for news_list in news_data['all_news'].dropna():
            if isinstance(news_list, list):
                all_news.extend(news_list)
            elif isinstance(news_list, str) and news_list.strip():
                all_news.append(news_list)

        # Debug: Show what news we found
        st.write(f"📰 Found {len(all_news)} news articles for sentiment analysis")
        if len(all_news) > 0:
            st.write("Sample news:", all_news[:3])  # Show first 3 news items

        news_sentiment = analyze_news_sentiment(all_news)
        create_news_sentiment_visualization(news_sentiment, symbol or "Stock")

    # Create enhanced price chart
    create_enhanced_price_chart(df, symbol or "Stock", news_sentiment)

    # --- Trend Template Analysis ---
    with st.expander("📈 Mark Minervini's Trend Template"):
        criteria_met, criteria_dict = trend_template_filter(df)

    # --- Investment Strategy Checks ---
    with st.expander("🔍 Investment Strategy Checks"):
        col1, col2, col3 = st.columns(3)

        with col1:
            st.markdown("#### Consolidation/Breakout Check")
            consolidation_result = consolidation_breakout_check(df)

        with col2:
            st.markdown("#### Candlestick Pattern Check")
            candlestick_identify(df)

        with col3:
            st.markdown("#### VCP Pattern Check")
            vcp_result = VCP_detection(df)

    # --- Vietnamese Market Analysis if symbol provided ---
    if symbol:
        with st.expander("🏢 Market Analysis"):
            market_analysis = vietnamese_market_analysis(symbol)

    # --- Price Prediction if symbol provided ---
    if symbol:
        with st.expander("🔮 AI Price Prediction"):
            from prediction_models import create_prediction_dashboard
            create_prediction_dashboard(symbol)

    # --- Overall Analysis Summary ---
    st.subheader("📋 Overall Analysis Summary")

    # Enhanced recommendation logic
    bullish_signals = 0
    bearish_signals = 0

    # Technical analysis signals
    if criteria_met >= 5:
        bullish_signals += 2
    elif criteria_met <= 2:
        bearish_signals += 2

    # News sentiment signals
    if news_sentiment:
        if news_sentiment['sentiment_label'] == 'Positive':
            bullish_signals += 1
        elif news_sentiment['sentiment_label'] == 'Negative':
            bearish_signals += 1

    # Pattern signals
    if 'consolidation_result' in locals():
        if consolidation_result == "Breakout":
            bullish_signals += 1
        elif consolidation_result == "Consolidating":
            bullish_signals += 0.5

    # Final recommendation
    total_signals = bullish_signals + bearish_signals
    if total_signals > 0:
        bullish_ratio = bullish_signals / total_signals

        if bullish_ratio >= 0.7:
            st.markdown(f'<h3 style="color:#33ff33;font-size:24px;">{"🚀 Strong Bullish Signal"}</h3>', unsafe_allow_html=True)
            st.success("Multiple indicators suggest strong upward momentum. Consider this for your watchlist.")
        elif bullish_ratio >= 0.5:
            st.markdown(f'<h3 style="color:#4ECDC4;font-size:24px;">{"📈 Bullish Trend"}</h3>', unsafe_allow_html=True)
            st.info("Indicators lean bullish. Monitor for entry opportunities.")
        elif bullish_ratio <= 0.3:
            st.markdown(f'<h3 style="color:#ff0d00;font-size:24px;">{"📉 Bearish Trend"}</h3>', unsafe_allow_html=True)
            st.warning("Multiple bearish signals detected. Exercise caution.")
        else:
            st.markdown(f'<h3 style="color:#b3aaaa;font-size:24px;">{"⚖️ Neutral/Mixed Signals"}</h3>', unsafe_allow_html=True)
            st.info("Mixed signals detected. Wait for clearer direction.")
    else:
        st.markdown(f'<h3 style="color:#b3aaaa;font-size:24px;">{"❓ Insufficient Data"}</h3>', unsafe_allow_html=True)
        st.warning("Not enough data for reliable analysis.")

    # Display signal breakdown
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Bullish Signals", f"{bullish_signals:.1f}")
    with col2:
        st.metric("Bearish Signals", f"{bearish_signals:.1f}")
    with col3:
        if total_signals > 0:
            st.metric("Bullish Ratio", f"{bullish_ratio:.1%}")
        else:
            st.metric("Bullish Ratio", "N/A")
