import streamlit as st

def UI():
    st.markdown('''
        <style>
        @import url('https://fonts.googleapis.com/css?family=Heebo'); 
        @import url('https://fonts.googleapis.com/css?family=Heebo:400,600,800,900');  

        body * { 
            -webkit-font-smoothing: subpixel-antialiased !important; 
            text-rendering:optimizeLegibility !important;
        }

        body hr {
            border-bottom: 1.5px solid rgba(23, 48, 28, 0.5); 
        }

        div[data-testid="stToolbarActions"] {
            visibility:hidden;
        }

        footer {visibility: hidden;}

        div[data-baseweb="tab-panel"] {
            padding-top: 2rem;
        }

        div.stButton > button:first-child {
            width: 166px !important;
            background-color: rgba(23, 48, 28, 0.95) !important;
            color: #F6F4F0 !important;
            margin-top: 10px !important;
        }
                
        div.stButton p {
            font-family: "Heebo";
            font-weight:600;
            font-size: 15px;
            letter-spacing: 0.25px;
            padding-top: 1px;
        }

        /* Set the sidebar width */
        [data-testid="stSidebar"][aria-expanded="true"] {
        min-width: 220px !important;
        max-width: 220px !important;
        }
        [data-testid="stSidebar"][aria-expanded="false"] {
            min-width: 300px !important;
            max-width: 300px !important;
        }

        /* Set the width of the dataframe container */
        /* Note: The specific data-testid may vary between Streamlit versions */
        div[data-testid="stDataFrameContainer"] {
            max-width: 1500px !important;
        }
        </style>
        ''', unsafe_allow_html=True)