from vnstock import Vnstock
import streamlit as st
import datetime
stock = Vnstock().stock(symbol='VND', source='TCBS')
today = datetime.datetime.now()
data = stock.listing.all_symbols()['symbol'].sort_values()
st.write(data.head())
stock_symbol = st.sidebar.selectbox("Stock Symbol", stock.listing.all_symbols()['symbol'])
start_date = st.sidebar.date_input("Start Date", datetime.date(2024, 1, 1),format='YYYY-MM-DD')
start_date = start_date.strftime('%Y-%m-%d')
end_date = st.sidebar.date_input("End Date", today, format='YYYY-MM-DD')
end_date = end_date.strftime('%Y-%m-%d')
stock_df = stock.quote.history(symbol=stock_symbol, start=start_date, end=end_date)
st.write(stock_df.tail())