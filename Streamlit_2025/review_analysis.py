import streamlit as st
import pandas as pd

# ------------------------------
# SCRAPERS - App Store & Google Play
# ------------------------------

from google_play_scraper import Sort, reviews
def fetch_google_play_reviews(app_url, num_reviews):
    """
    Fetches reviews from a Google Play app.

    Parameters:
        app_url (str): The URL of the Google Play app.
        num_reviews (int): The number of reviews to fetch.

    Returns:
        pd.DataFrame: DataFrame containing the reviews.
    """
    # Extract the app ID from the URL
    app_id = app_url.split('id=')[-1]

    # Fetch reviews
    result, _ = reviews(
        app_id,
        lang='vi',  # Language set to Vietnamese
        country='vn',  # Country set to Vietnam
        sort=Sort.MOST_RELEVANT,
        count=num_reviews
    )

    # Convert to DataFrame
    df = pd.DataFrame(result)

    # For consistency in sentiment analysis, rename the review column to "reviews"
    if "content" in df.columns:
        df.rename(columns={"content": "review"}, inplace=True)

    return df

from app_store_scraper import AppStore
def fetch_app_store_reviews(app_id, num_reviews):
    """
    Fetches reviews from an App Store app.

    Parameters:
        app_id (str): The App Store app ID.
        num_reviews (int): The number of reviews to fetch.

    Returns:
        pd.DataFrame: DataFrame containing the reviews.
    """
    # Initialize the AppStore object
    app = AppStore(country="vn", app_name="", app_id=app_id)

    # Fetch reviews
    app.review(how_many=num_reviews)

    # Convert to DataFrame
    df = pd.DataFrame(app.reviews)
    return df

# ------------------------------
# SCRAPERS - Sentiment Analysis
# ------------------------------

import torch
import numpy as np
from transformers import AutoModelForSequenceClassification, AutoTokenizer, AutoConfig

# === Model Loading Functions ===
@st.cache_resource(show_spinner=False)
def load_sentiment_model(model_path):
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    config = AutoConfig.from_pretrained(model_path)
    model = AutoModelForSequenceClassification.from_pretrained(model_path)
    return tokenizer, config, model

# === Vietnamese Model Functions ===
def analyze_sentiment_vietnamese(text, tokenizer, config, model):
    if not isinstance(text, str) or text.strip() == "":
        return {"POS": 0, "NEU": 0, "NEG": 0}
    inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True, max_length=512)
    with torch.no_grad():
        outputs = model(**inputs)
    scores = outputs.logits.softmax(dim=-1).cpu().numpy()[0]
    ranking = np.argsort(scores)[::-1]
    return {config.id2label[ranking[i]]: np.round(float(scores[ranking[i]]), 4)
            for i in range(len(scores))}

def extract_dominant_sentiment(sentiment_scores):
    if not sentiment_scores:
        return "Unknown"
    dominant = max(sentiment_scores, key=sentiment_scores.get)
    mapping = {"POS": "Positive", "NEU": "Neutral", "NEG": "Negative"}
    return mapping.get(dominant, "Unknown")

def process_sentiment_dataframe(df, tokenizer, config, model):
    sentiment_data = df["review"].apply(lambda text: analyze_sentiment_vietnamese(text, tokenizer, config, model))
    df["Positive"] = sentiment_data.apply(lambda x: x.get("POS", 0))
    df["Neutral"] = sentiment_data.apply(lambda x: x.get("NEU", 0))
    df["Negative"] = sentiment_data.apply(lambda x: x.get("NEG", 0))
    df["Dominant Sentiment"] = sentiment_data.apply(extract_dominant_sentiment)
    # For simplicity, assign default confidence of 100 to every row.
    df["Confidence"] = 100.0
    return df

# === Multilingual Model Function ===
def predict_sentiment_multilingual(text, tokenizer, model):
    if not isinstance(text, str) or text.strip() == "":
        return pd.Series(["Neutral", 100.0])
    inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True, max_length=512).to("cpu")
    with torch.no_grad():
        outputs = model(**inputs)
    probabilities = torch.nn.functional.softmax(outputs.logits, dim=-1).cpu().numpy()[0]
    sentiment_map = {0: "Very Negative", 1: "Negative", 2: "Neutral", 3: "Positive", 4: "Very Positive"}
    probabilities_percent = [round(p * 100, 2) for p in probabilities]
    dominant_index = probabilities.argmax()
    dominant_sentiment = sentiment_map[dominant_index]
    dominant_probability_percent = probabilities_percent[dominant_index]
    return pd.Series([dominant_sentiment, dominant_probability_percent])

# === Mapping Functions ===
# For multilingual (5 segments)
def sentiment_to_position_5(label: str) -> int:
    mapping = {
        "Very Negative": 10,
        "Negative": 30,
        "Neutral": 50,
        "Positive": 70,
        "Very Positive": 90,
    }
    return mapping.get(label, 50)

# For Vietnamese (3 segments)
def sentiment_to_position_3(label: str) -> int:
    mapping = {
        "Negative": 17,  # mid of 0-33
        "Neutral": 50,   # mid of 33-67
        "Positive": 83   # mid of 67-100
    }
    return mapping.get(label, 50)

# === Visualization Function ===
import plotly.graph_objects as go
def plot_sentiment_scale(overall_score, sentiment_label, sentiment_score=0.0, segments=None):
    fig = go.Figure()

    if segments is None:
        # Default 5-segment configuration.
        segments = [
            (0, 20, "Very Negative"),
            (20, 40, "Negative"),
            (40, 60, "Neutral"),
            (60, 80, "Positive"),
            (80, 100, "Very Positive")
        ]
        tickvals = [10, 30, 50, 70, 90]
    else:
        # Use provided segments. We'll compute tickvals as the midpoint of each segment.
        tickvals = [ (low + high)/2 for low, high, label in segments ]

    # Define ticktext from segments.
    ticktext = [ label for low, high, label in segments ]
    # Define colors for each segment (adjust as needed).
    if len(segments) == 5:
        colors = ["#d20000", "#fa9600", "#ffc72c", "#29a394", "#008572"]
    elif len(segments) == 3:
        colors = ["#fa9600", "#ffc72c", "#29a394"]
    else:
        # Fallback
        colors = ["gray"] * len(segments)

    # Draw segments.
    for i, (x0, x1, label) in enumerate(segments):
        fig.add_shape(
            type="rect", x0=x0, x1=x1, y0=0, y1=1,
            fillcolor=colors[i], opacity=0.7, line_width=0
        )

    # Add pointer.
    fig.add_trace(go.Scatter(
        x=[overall_score],
        y=[0.5],
        mode="markers",
        marker=dict(
            color="black",
            size=16,
            symbol="triangle-down"
        ),
        hovertext=[f"Overall Sentiment: {sentiment_label}<br>Score: {overall_score:.2f}"],
        hoverinfo="text",
    ))

    fig.update_layout(
        xaxis=dict(
            range=[0, 100],
            tickmode='array',
            tickvals=tickvals,
            ticktext=ticktext,
            showgrid=False,
            zeroline=False
        ),
        yaxis=dict(
            range=[0, 1],
            showgrid=False,
            zeroline=False,
            visible=False
        ),
        margin=dict(l=20, r=20, t=20, b=20),
        height=150,
        plot_bgcolor='white',
        showlegend=False
    )
    return fig