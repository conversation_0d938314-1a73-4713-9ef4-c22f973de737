# 📊 Enhanced Vietnamese Stock Analysis Platform

A comprehensive Streamlit application for analyzing Vietnamese stocks with advanced market analysis, AI-powered predictions, and enhanced visualizations.

## 🚀 New Features Added

### 🏢 Market Analysis Dashboard
- **Performance Categories**: Visual breakdown of stocks by ceiling, increase, unchanged, decrease, and floor categories
- **Sector Analysis**: Performance analysis grouped by Vietnamese market sectors (Banking, Real Estate, Technology, etc.)
- **Market Breadth Indicators**: Advance/decline ratios, market strength gauge, and breadth metrics
- **Sector Heatmap**: Interactive heatmap showing sector performance metrics
- **Real-time Data**: Parallel data fetching for improved performance

### 🔮 AI-Powered Price Prediction
- **Machine Learning Models**: Random Forest, Gradient Boosting, and Logistic Regression
- **Technical Features**: 20+ technical indicators including RSI, MACD, Bollinger Bands
- **Probability Prediction**: 1-month price increase probability with confidence scores
- **Feature Importance**: Analysis of which factors most influence predictions
- **Model Performance**: Accuracy, precision, and recall metrics

### 📰 Enhanced News Sentiment Analysis
- **Sentiment Scoring**: TextBlob-based sentiment analysis of news articles
- **Visual Sentiment**: Pie charts and gauge charts for sentiment distribution
- **Integration**: Sentiment data integrated into overall stock analysis
- **Multi-source**: Analysis across all Vietstock news categories

### 📈 Enhanced Visualizations
- **Interactive Charts**: Candlestick charts with technical indicators overlay
- **Multi-panel Displays**: Price, volume, and technical indicators in subplots
- **Bollinger Bands**: Dynamic support and resistance levels
- **Moving Averages**: 20-day and 50-day moving averages
- **RSI Indicators**: Overbought/oversold levels with visual markers

### 🔍 Improved Analysis Engine
- **Multi-signal Analysis**: Combines technical, sentiment, and pattern signals
- **Enhanced Recommendations**: Weighted scoring system for buy/sell signals
- **Signal Breakdown**: Detailed breakdown of bullish vs bearish signals
- **Risk Assessment**: Comprehensive risk metrics and warnings

## 📁 File Structure

```
Streamlit_2025/
├── app.py                    # Main application with enhanced navigation
├── stock_analysis.py         # Enhanced technical analysis with sentiment
├── market_analysis.py        # NEW: Market-wide analysis dashboard
├── prediction_models.py      # NEW: AI prediction models
├── requirements.txt          # Updated dependencies
├── ui_ux.py                 # UI/UX components
├── general_search.py        # Search functionality
├── review_analysis.py       # App review analysis
├── crawl_news.py           # News crawling
└── README.md               # This file
```

## 🛠️ Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Streamlit_2025
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Download NLTK data for TextBlob** (if needed)
   ```python
   import nltk
   nltk.download('punkt')
   nltk.download('brown')
   ```

4. **Run the application**
   ```bash
   streamlit run app.py
   ```

## 🎯 How to Use

### Market Analysis
1. Navigate to "Stock Analysis" → "Market" tab
2. Click "Refresh Market Data" to fetch latest data
3. Explore:
   - Market overview with performance categories
   - Sector performance heatmap
   - Market breadth indicators
   - Export data as CSV

### Single Stock Analysis
1. Navigate to "Stock Analysis" → "Single" tab
2. Select a stock symbol and date range
3. Click "Get Data" to fetch stock and news data
4. Explore enhanced features:
   - **News Sentiment**: Automatic sentiment analysis of related news
   - **Enhanced Charts**: Interactive technical analysis charts
   - **AI Prediction**: Machine learning price predictions
   - **Technical Analysis**: Mark Minervini's trend template and patterns
   - **Market Analysis**: Volume and price action analysis

### AI Price Prediction
1. In Single stock analysis, expand "AI Price Prediction"
2. Select model type (Random Forest recommended)
3. Click "Train Model" to create predictions
4. View:
   - Model accuracy and performance metrics
   - Feature importance analysis
   - Probability gauge for 1-month price increase
   - Model explanation and interpretation

## 📊 Key Metrics Explained

### Market Categories
- **Ceiling**: Stocks hitting daily price ceiling (+7% limit)
- **Increase**: Stocks with positive price movement
- **Unchanged**: Stocks with no price change
- **Decrease**: Stocks with negative price movement
- **Floor**: Stocks hitting daily price floor (-7% limit)

### Prediction Probability
- **>70%**: High probability of 5%+ increase in 30 days
- **50-70%**: Moderate probability of increase
- **<50%**: Low probability of significant increase

### Sentiment Scores
- **Positive (>0.1)**: Bullish news sentiment
- **Neutral (-0.1 to 0.1)**: Neutral news sentiment
- **Negative (<-0.1)**: Bearish news sentiment

## 🔧 Technical Details

### Data Sources
- **Stock Data**: VnStock API (TCBS/VCI sources)
- **News Data**: Vietstock.vn (multiple categories)
- **Market Data**: Real-time fetching with parallel processing

### Machine Learning Features
- Price ratios and moving average ratios
- Volatility indicators (5-day, 20-day)
- Volume analysis and ratios
- RSI (Relative Strength Index)
- MACD and signal lines
- Bollinger Bands position
- Support/resistance distances

### Performance Optimizations
- Parallel data fetching for market analysis
- Session state caching for expensive operations
- Efficient DataFrame operations
- Lazy loading of ML models

## 🚨 Important Notes

### Disclaimers
- **Educational Purpose**: This tool is for educational and research purposes only
- **Not Financial Advice**: Do not use as sole basis for investment decisions
- **Risk Warning**: Past performance does not guarantee future results
- **Due Diligence**: Always conduct your own research and consult financial advisors

### Limitations
- Predictions based on historical technical data only
- News sentiment analysis is basic (TextBlob-based)
- Market data limited to top 100 stocks for performance
- Real-time data depends on API availability

## 🔄 Future Enhancements

### Planned Features
- **Fundamental Analysis**: P/E ratios, financial statements integration
- **Advanced ML**: LSTM neural networks for time series prediction
- **Real-time Alerts**: Price and pattern alerts
- **Portfolio Tracking**: Multi-stock portfolio analysis
- **Advanced Sentiment**: Vietnamese language sentiment models
- **Options Analysis**: Options pricing and Greeks
- **Backtesting**: Strategy backtesting framework

### Technical Improvements
- Database integration for historical data storage
- API rate limiting and error handling
- Mobile-responsive design
- Performance monitoring and optimization
- Unit testing and CI/CD pipeline

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For questions, issues, or feature requests:
- Create an issue in the repository
- Contact the development team
- Check the documentation for troubleshooting

## 📄 License

This project is for educational purposes. Please respect data source terms of service and applicable financial regulations.

---

**Happy Trading! 📈🚀**

*Remember: Always invest responsibly and never risk more than you can afford to lose.*