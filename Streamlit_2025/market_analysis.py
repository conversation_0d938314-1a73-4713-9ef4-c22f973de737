import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from vnstock import Vnstock
import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
from typing import Dict, List, Tuple
import time

# Vietnamese stock market sectors mapping
VIETNAMESE_SECTORS = {
    'Banking': ['VCB', 'BID', 'CTG', 'TCB', 'VPB', 'MBB', 'STB', 'HDB', 'TPB', 'ACB'],
    'Real Estate': ['VHM', 'VIC', 'NVL', 'VRE', 'PDR', 'DXG', 'KDH', 'DIG', 'HDG', 'CEO'],
    'Manufacturing': ['VNM', 'MSN', 'MWG', 'FPT', 'VJC', 'GAS', 'PLX', 'POW', 'REE', 'GEX'],
    'Technology': ['FPT', 'CMG', 'ELC', 'VGI', 'ITD', 'SAM', 'VNG', 'BASE', 'SVC', 'VCS'],
    'Consumer Goods': ['VNM', 'MSN', 'MWG', 'PNJ', 'DGW', 'FRT', 'VHC', 'KDC', 'VCF', 'MCH'],
    'Energy': ['GAS', 'PLX', 'POW', 'PVD', 'PVS', 'BSR', 'PVT', 'PVC', 'PVB', 'OIL'],
    'Healthcare': ['DHG', 'IMP', 'PME', 'TNH', 'DBD', 'AMV', 'SPM', 'DP3', 'MKV', 'TRA'],
    'Transportation': ['VJC', 'HVN', 'VOS', 'GMD', 'VSC', 'MVN', 'PVT', 'SCS', 'TCL', 'VIP']
}

def get_stock_performance_category(current_price: float, previous_close: float, 
                                 ceiling_price: float, floor_price: float) -> str:
    """
    Categorize stock performance based on Vietnamese market rules
    
    Args:
        current_price: Current stock price
        previous_close: Previous day's closing price
        ceiling_price: Daily ceiling price (usually +7% for most stocks)
        floor_price: Daily floor price (usually -7% for most stocks)
    
    Returns:
        Category: 'ceiling', 'increase', 'unchanged', 'decrease', 'floor'
    """
    if current_price >= ceiling_price:
        return 'ceiling'
    elif current_price <= floor_price:
        return 'floor'
    elif current_price > previous_close:
        return 'increase'
    elif current_price < previous_close:
        return 'decrease'
    else:
        return 'unchanged'

def fetch_market_data_parallel(symbols: List[str], max_workers: int = 5) -> pd.DataFrame:
    """
    Fetch current market data for multiple symbols in parallel (optimized)

    Args:
        symbols: List of stock symbols
        max_workers: Maximum number of parallel workers (reduced for stability)

    Returns:
        DataFrame with market data
    """
    def fetch_single_stock(symbol):
        try:
            stock = Vnstock().stock(symbol=symbol, source='TCBS')
            # Get only recent data (last 5 days) for performance
            end_date = datetime.datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.datetime.now() - datetime.timedelta(days=5)).strftime('%Y-%m-%d')

            current_data = stock.quote.history(symbol=symbol, start=start_date, end=end_date)

            if not current_data.empty:
                latest = current_data.iloc[-1]
                previous = current_data.iloc[-2] if len(current_data) > 1 else latest

                # Calculate ceiling and floor (7% rule for most Vietnamese stocks)
                ceiling = previous['close'] * 1.07
                floor = previous['close'] * 0.93

                category = get_stock_performance_category(
                    latest['close'], previous['close'], ceiling, floor
                )

                # Get sector
                sector = 'Other'
                for sec, syms in VIETNAMESE_SECTORS.items():
                    if symbol in syms:
                        sector = sec
                        break

                return {
                    'symbol': symbol,
                    'current_price': latest['close'],
                    'previous_close': previous['close'],
                    'change_pct': ((latest['close'] - previous['close']) / previous['close']) * 100,
                    'volume': latest['volume'],
                    'category': category,
                    'sector': sector,
                    'ceiling': ceiling,
                    'floor': floor
                }
        except Exception as e:
            # Silently skip errors to avoid cluttering the UI
            return None

    results = []
    progress_bar = st.progress(0)
    status_text = st.empty()

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_symbol = {executor.submit(fetch_single_stock, symbol): symbol for symbol in symbols}

        completed = 0
        for future in as_completed(future_to_symbol):
            result = future.result()
            if result:
                results.append(result)

            completed += 1
            progress = completed / len(symbols)
            progress_bar.progress(progress)
            status_text.text(f"Fetched data for {completed}/{len(symbols)} stocks...")

    progress_bar.empty()
    status_text.empty()

    return pd.DataFrame(results)

def create_sample_market_data() -> pd.DataFrame:
    """
    Create sample market data for demonstration when API is slow
    """
    import random

    # Get all symbols from sectors
    all_symbols = []
    for sector_symbols in VIETNAMESE_SECTORS.values():
        all_symbols.extend(sector_symbols[:3])  # Take first 3 from each sector

    sample_data = []
    categories = ['ceiling', 'increase', 'unchanged', 'decrease', 'floor']

    for symbol in all_symbols:
        # Get sector
        sector = 'Other'
        for sec, syms in VIETNAMESE_SECTORS.items():
            if symbol in syms:
                sector = sec
                break

        # Generate sample data
        previous_close = random.uniform(10, 200)
        change_pct = random.uniform(-7, 7)
        current_price = previous_close * (1 + change_pct/100)

        # Determine category based on change
        if change_pct >= 6.5:
            category = 'ceiling'
        elif change_pct <= -6.5:
            category = 'floor'
        elif change_pct > 0:
            category = 'increase'
        elif change_pct < 0:
            category = 'decrease'
        else:
            category = 'unchanged'

        sample_data.append({
            'symbol': symbol,
            'current_price': current_price,
            'previous_close': previous_close,
            'change_pct': change_pct,
            'volume': random.randint(100000, 10000000),
            'category': category,
            'sector': sector,
            'ceiling': previous_close * 1.07,
            'floor': previous_close * 0.93
        })

    return pd.DataFrame(sample_data)

def create_market_overview_dashboard(market_data: pd.DataFrame) -> None:
    """
    Create comprehensive market overview dashboard
    
    Args:
        market_data: DataFrame with market data
    """
    st.header("📊 Market Overview Dashboard")
    
    if market_data.empty:
        st.warning("No market data available")
        return
    
    # Overall market statistics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_stocks = len(market_data)
        st.metric("Total Stocks", total_stocks)
    
    with col2:
        avg_change = market_data['change_pct'].mean()
        st.metric("Avg Change %", f"{avg_change:.2f}%")
    
    with col3:
        advancing = len(market_data[market_data['change_pct'] > 0])
        st.metric("Advancing", advancing)
    
    with col4:
        declining = len(market_data[market_data['change_pct'] < 0])
        st.metric("Declining", declining)
    
    # Performance category breakdown
    st.subheader("📈 Performance Categories")
    
    category_counts = market_data['category'].value_counts()
    
    # Create pie chart for performance categories
    fig_pie = px.pie(
        values=category_counts.values,
        names=category_counts.index,
        title="Stock Performance Distribution",
        color_discrete_map={
            'ceiling': '#FF6B6B',
            'increase': '#4ECDC4', 
            'unchanged': '#45B7D1',
            'decrease': '#FFA07A',
            'floor': '#FF4757'
        }
    )
    
    col1, col2 = st.columns(2)
    with col1:
        st.plotly_chart(fig_pie, use_container_width=True)
    
    with col2:
        # Performance by sector
        sector_performance = market_data.groupby(['sector', 'category']).size().unstack(fill_value=0)
        
        fig_sector = px.bar(
            sector_performance,
            title="Performance by Sector",
            labels={'value': 'Number of Stocks', 'index': 'Sector'},
            color_discrete_map={
                'ceiling': '#FF6B6B',
                'increase': '#4ECDC4', 
                'unchanged': '#45B7D1',
                'decrease': '#FFA07A',
                'floor': '#FF4757'
            }
        )
        fig_sector.update_layout(xaxis_tickangle=-45)
        st.plotly_chart(fig_sector, use_container_width=True)

def create_sector_specific_analysis(market_data: pd.DataFrame, sector_name: str) -> None:
    """
    Create detailed analysis for a specific sector

    Args:
        market_data: DataFrame with market data for the sector
        sector_name: Name of the sector being analyzed
    """
    st.subheader(f"🎯 Detailed {sector_name} Sector Analysis")

    if market_data.empty:
        st.warning(f"No data available for {sector_name} sector analysis")
        return

    # Sector performance metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        avg_change = market_data['change_pct'].mean()
        st.metric("Avg Change %", f"{avg_change:.2f}%")

    with col2:
        best_performer = market_data.loc[market_data['change_pct'].idxmax()]
        st.metric("Best Performer", best_performer['symbol'], f"+{best_performer['change_pct']:.2f}%")

    with col3:
        worst_performer = market_data.loc[market_data['change_pct'].idxmin()]
        st.metric("Worst Performer", worst_performer['symbol'], f"{worst_performer['change_pct']:.2f}%")

    with col4:
        total_volume = market_data['volume'].sum()
        st.metric("Total Volume", f"{total_volume:,.0f}")

    # Individual stock performance chart
    fig_stocks = px.bar(
        market_data.sort_values('change_pct', ascending=True),
        x='change_pct',
        y='symbol',
        orientation='h',
        title=f"{sector_name} Sector - Individual Stock Performance",
        labels={'change_pct': 'Change %', 'symbol': 'Stock Symbol'},
        color='change_pct',
        color_continuous_scale='RdYlGn'
    )

    fig_stocks.update_layout(height=max(400, len(market_data) * 25))
    st.plotly_chart(fig_stocks, use_container_width=True)

    # Performance distribution
    col1, col2 = st.columns(2)

    with col1:
        # Category distribution
        category_counts = market_data['category'].value_counts()
        fig_category = px.pie(
            values=category_counts.values,
            names=category_counts.index,
            title=f"{sector_name} - Performance Categories",
            color_discrete_map={
                'ceiling': '#FF6B6B',
                'increase': '#4ECDC4',
                'unchanged': '#45B7D1',
                'decrease': '#FFA07A',
                'floor': '#FF4757'
            }
        )
        st.plotly_chart(fig_category, use_container_width=True)

    with col2:
        # Volume vs Performance scatter
        fig_scatter = px.scatter(
            market_data,
            x='volume',
            y='change_pct',
            size='current_price',
            hover_data=['symbol'],
            title=f"{sector_name} - Volume vs Performance",
            labels={'volume': 'Trading Volume', 'change_pct': 'Change %'}
        )
        st.plotly_chart(fig_scatter, use_container_width=True)

    # Top and bottom performers table
    st.subheader(f"📊 {sector_name} Sector - Stock Rankings")

    # Sort by performance
    sorted_data = market_data.sort_values('change_pct', ascending=False)

    col1, col2 = st.columns(2)

    with col1:
        st.write("🏆 **Top Performers**")
        top_performers = sorted_data.head(min(5, len(sorted_data)))
        display_cols = ['symbol', 'current_price', 'change_pct', 'volume', 'category']
        st.dataframe(top_performers[display_cols], use_container_width=True, hide_index=True)

    with col2:
        st.write("📉 **Bottom Performers**")
        bottom_performers = sorted_data.tail(min(5, len(sorted_data)))
        st.dataframe(bottom_performers[display_cols], use_container_width=True, hide_index=True)

def create_sector_heatmap(market_data: pd.DataFrame) -> None:
    """
    Create sector performance heatmap
    
    Args:
        market_data: DataFrame with market data
    """
    st.subheader("🔥 Sector Performance Heatmap")
    
    if market_data.empty:
        st.warning("No data available for heatmap")
        return
    
    # Calculate sector metrics
    sector_metrics = market_data.groupby('sector').agg({
        'change_pct': ['mean', 'std', 'count'],
        'volume': 'sum'
    }).round(2)
    
    sector_metrics.columns = ['Avg_Change_%', 'Volatility', 'Stock_Count', 'Total_Volume']
    sector_metrics = sector_metrics.reset_index()
    
    # Create heatmap
    fig = px.imshow(
        sector_metrics[['Avg_Change_%', 'Volatility', 'Stock_Count']].T,
        x=sector_metrics['sector'],
        y=['Avg Change %', 'Volatility', 'Stock Count'],
        color_continuous_scale='RdYlGn',
        aspect='auto',
        title="Sector Performance Metrics Heatmap"
    )
    
    fig.update_layout(height=400)
    st.plotly_chart(fig, use_container_width=True)
    
    # Display sector metrics table
    st.subheader("📋 Sector Metrics Table")
    st.dataframe(sector_metrics, use_container_width=True)

def create_market_breadth_indicators(market_data: pd.DataFrame) -> None:
    """
    Create market breadth indicators
    
    Args:
        market_data: DataFrame with market data
    """
    st.subheader("📊 Market Breadth Indicators")
    
    if market_data.empty:
        st.warning("No data available for breadth indicators")
        return
    
    # Calculate breadth metrics
    advancing = len(market_data[market_data['change_pct'] > 0])
    declining = len(market_data[market_data['change_pct'] < 0])
    unchanged = len(market_data[market_data['change_pct'] == 0])
    
    advance_decline_ratio = advancing / declining if declining > 0 else float('inf')
    
    # Create breadth visualization
    col1, col2 = st.columns(2)
    
    with col1:
        # Advance/Decline chart
        breadth_data = pd.DataFrame({
            'Category': ['Advancing', 'Declining', 'Unchanged'],
            'Count': [advancing, declining, unchanged],
            'Percentage': [advancing/len(market_data)*100, declining/len(market_data)*100, unchanged/len(market_data)*100]
        })
        
        fig_breadth = px.bar(
            breadth_data,
            x='Category',
            y='Count',
            color='Category',
            title="Advance/Decline Breadth",
            color_discrete_map={
                'Advancing': '#4ECDC4',
                'Declining': '#FF6B6B',
                'Unchanged': '#45B7D1'
            }
        )
        st.plotly_chart(fig_breadth, use_container_width=True)
    
    with col2:
        # Market strength gauge
        market_strength = (advancing - declining) / len(market_data) * 100
        
        fig_gauge = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = market_strength,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Market Strength (%)"},
            delta = {'reference': 0},
            gauge = {
                'axis': {'range': [-100, 100]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [-100, -20], 'color': "lightgray"},
                    {'range': [-20, 20], 'color': "gray"},
                    {'range': [20, 100], 'color': "lightgreen"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 0
                }
            }
        ))
        
        fig_gauge.update_layout(height=300)
        st.plotly_chart(fig_gauge, use_container_width=True)
    
    # Display key metrics
    st.subheader("🔢 Key Breadth Metrics")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("A/D Ratio", f"{advance_decline_ratio:.2f}")
    
    with col2:
        st.metric("Market Strength", f"{market_strength:.1f}%")
    
    with col3:
        new_highs = len(market_data[market_data['change_pct'] >= 5])  # Assuming 5% as significant gain
        st.metric("Strong Gainers", new_highs)
    
    with col4:
        new_lows = len(market_data[market_data['change_pct'] <= -5])  # Assuming -5% as significant loss
        st.metric("Strong Decliners", new_lows)

def run_market_analysis() -> None:
    """
    Main function to run comprehensive market analysis
    """
    # st.title("🏢 Vietnamese Stock Market Analysis")

    # Analysis mode selection
    col1, col2 = st.columns(2)

    with col1:
        # Check if cached data exists
        has_cached_data = 'market_data' in st.session_state and st.session_state.market_data is not None

        if has_cached_data:
            cache_info = f"Cached Data ({len(st.session_state.market_data)} stocks)"
            options = ["Quick Demo (Sample Data)", "Live Data (Slower)", "Sector Analysis", cache_info]
        else:
            options = ["Quick Demo (Sample Data)", "Live Data (Slower)", "Sector Analysis"]

        analysis_mode = st.selectbox(
            "Select Analysis Mode",
            options,
            help="Choose analysis type: Demo data, Live data, Sector-specific analysis, or Cached data"
        )

    with col2:
        if analysis_mode == "Live Data (Slower)":
            num_stocks = st.slider("Number of stocks to analyze", 10, 50, 25,
                                 help="More stocks = more accurate but slower")
        elif analysis_mode == "Sector Analysis":
            # Show sector selection dropdown
            available_sectors = list(VIETNAMESE_SECTORS.keys())
            selected_sector = st.selectbox(
                "Select Sector",
                available_sectors,
                help="Choose a specific sector to analyze"
            )
        else:
            num_stocks = 25

    # Add refresh button
    if st.button("🔄 Refresh Market Data", type="primary"):
        st.session_state.market_data = None

    # Fetch market data based on mode
    if 'market_data' not in st.session_state or st.session_state.market_data is None:

        if analysis_mode == "Quick Demo (Sample Data)":
            with st.spinner("Generating sample market data..."):
                market_data = create_sample_market_data()
                st.info("📊 Using sample data for demonstration. Switch to 'Live Data' for real market data.")
                st.session_state.market_data = market_data

        elif analysis_mode == "Sector Analysis":
            # Get symbols for selected sector
            sector_symbols = VIETNAMESE_SECTORS.get(selected_sector, [])

            if not sector_symbols:
                st.error(f"No symbols found for sector: {selected_sector}")
                return

            try:
                with st.spinner(f"Fetching data for {len(sector_symbols)} stocks in {selected_sector} sector..."):
                    market_data = fetch_market_data_parallel(sector_symbols, max_workers=3)

                    # Filter to only include the selected sector (in case some symbols failed)
                    market_data = market_data[market_data['sector'] == selected_sector]

                    if market_data.empty:
                        st.warning(f"No data could be fetched for {selected_sector} sector. Using sample data.")
                        # Create sample data for this sector only
                        sample_data = create_sample_market_data()
                        market_data = sample_data[sample_data['sector'] == selected_sector]

                    st.session_state.market_data = market_data
                    st.success(f"✅ Fetched data for {len(market_data)} stocks in {selected_sector} sector")

            except Exception as e:
                st.error(f"Error fetching sector data: {str(e)}")
                st.info("Using sample data for sector analysis...")
                sample_data = create_sample_market_data()
                market_data = sample_data[sample_data['sector'] == selected_sector]
                st.session_state.market_data = market_data

        elif analysis_mode == "Live Data (Slower)":
            try:
                stock = Vnstock().stock(symbol='VND', source='TCBS')
                all_symbols = stock.listing.all_symbols()['symbol'].tolist()

                # Limit symbols for performance
                selected_symbols = all_symbols[:num_stocks]

                with st.spinner(f"Fetching live data for {len(selected_symbols)} stocks... This may take 1-2 minutes."):
                    market_data = fetch_market_data_parallel(selected_symbols, max_workers=3)
                    st.session_state.market_data = market_data

            except Exception as e:
                st.error(f"Error fetching live data: {str(e)}")
                st.info("Falling back to sample data...")
                market_data = create_sample_market_data()
                st.session_state.market_data = market_data

        else:  # Cached Data
            if 'market_data' in st.session_state and st.session_state.market_data is not None:
                market_data = st.session_state.market_data
                st.success(f"✅ Using cached data with {len(market_data)} stocks")
            else:
                st.warning("No cached data available. Please select 'Quick Demo' or 'Live Data' to fetch new data.")
                return
    else:
        market_data = st.session_state.market_data

    # Check if market_data is valid before proceeding
    if market_data is None or market_data.empty:
        st.error("No market data available. Please try a different analysis mode or refresh the data.")
        return

    # Show data info with sector-specific context
    if analysis_mode == "Sector Analysis":
        sector_name = market_data['sector'].iloc[0] if not market_data.empty else selected_sector
        st.info(f"🎯 Sector Analysis: {len(market_data)} stocks in {sector_name} sector")

        # Show sector-specific insights
        if not market_data.empty:
            avg_change = market_data['change_pct'].mean()
            sector_performance = "📈 Outperforming" if avg_change > 0 else "📉 Underperforming" if avg_change < -1 else "⚖️ Neutral"
            st.metric(f"{sector_name} Sector Performance", f"{avg_change:.2f}%", delta=sector_performance)
    else:
        st.info(f"📈 Analyzing {len(market_data)} stocks across {market_data['sector'].nunique()} sectors")

    # Create dashboard components
    create_market_overview_dashboard(market_data)

    st.divider()

    # Modify sector heatmap for sector analysis
    if analysis_mode == "Sector Analysis":
        create_sector_specific_analysis(market_data, selected_sector if 'selected_sector' in locals() else None)
    else:
        create_sector_heatmap(market_data)

    st.divider()

    create_market_breadth_indicators(market_data)

    # Export functionality
    st.subheader("📥 Export Data")
    csv = market_data.to_csv(index=False)
    st.download_button(
        label="Download Market Data as CSV",
        data=csv,
        file_name=f"market_analysis_{datetime.datetime.now().strftime('%Y%m%d_%H%M')}.csv",
        mime="text/csv",
    )

    # Show sample of data
    with st.expander("📋 View Raw Data"):
        st.dataframe(market_data, use_container_width=True)
